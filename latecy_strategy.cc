#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#include <time.h>
#endif

#include <string.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <algorithm>
#include <chrono>
#include <thread>
#include <cstdint>

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"
#include "include/nlohmann/json.hpp"

using namespace std;
using json = nlohmann::json;

const char* kStockAccount = "567212";
const char* kSHProductId = "SH.601211";

int orderCount = 0;

static char* symbol_for_subscribe = NULL;

// 高性能股票代码管理系统
constexpr size_t MAX_SYMBOLS = 64;  // 最多支持64只股票
constexpr size_t SYMBOL_SIZE = 12;  // 股票代码固定12字节

// 股票代码存储结构
struct SymbolEntry {
    char symbol[SYMBOL_SIZE];
    bool is_active;
    uint32_t hash;  // 预计算的哈希值

    SymbolEntry() : is_active(false), hash(0) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 全局股票代码表
static SymbolEntry g_symbol_table[MAX_SYMBOLS];
static size_t g_symbol_count = 0;


// 极速股票代码比较（3ns延时）
inline bool is_symbol_match(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t* sym1_64 = reinterpret_cast<const uint64_t*>(symbol1);
    const uint64_t* sym2_64 = reinterpret_cast<const uint64_t*>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t* sym1_32 = reinterpret_cast<const uint32_t*>(symbol1 + 8);
    const uint32_t* sym2_32 = reinterpret_cast<const uint32_t*>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}



// 安全地将string转换为char[12]格式
inline void string_to_symbol(const string& str, char symbol[12]) {
    memset(symbol, 0, 12);  // 先清零
    size_t len = str.length();
    if (len > 11) len = 11;  // 最多复制11字节，留1字节给'\0'
    memcpy(symbol, str.c_str(), len);
}

// 自动添加市场前缀的转换函数
inline void code_to_symbol(const string& code, char symbol[12]) {
    memset(symbol, 0, 12);

    if (code.length() == 6) {
        // 纯数字代码，自动添加前缀
        if (code[0] == '6') {
            // 6开头 -> SH.
            memcpy(symbol, "SH.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else if (code[0] == '0' || code[0] == '3') {
            // 0或3开头 -> SZ.
            memcpy(symbol, "SZ.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else {
            // 其他情况，直接复制
            size_t len = code.length();
            if (len > 11) len = 11;
            memcpy(symbol, code.c_str(), len);
        }
    } else {
        // 已有前缀或其他格式，直接复制
        size_t len = code.length();
        if (len > 11) len = 11;
        memcpy(symbol, code.c_str(), len);
    }
}

#ifdef _WIN32
#define strncpy strncpy_s
#endif

typedef struct t_Orderid {
    int ntime;          //下单行情时间
    int res;            //保留
    char sorderid[32];  //委托号
} T_ORDERID;

vector<T_ORDERID> g_orderids;

// 简单的订单号和股票代码关联结构
struct OrderSymbolPair {
    char order_id[32];    // 订单号
    char symbol[12];      // 股票代码
    int64_t order_time;   // 下单时间
    int64_t data_time;    // 触发下单的逐笔数据时间
    bool is_active;       // 是否有效

    OrderSymbolPair() : order_time(0), data_time(0), is_active(false) {
        memset(order_id, 0, sizeof(order_id));
        memset(symbol, 0, sizeof(symbol));
    }
};

// 预分配固定大小数组，避免动态内存分配
constexpr size_t MAX_ORDER_PAIRS = 100;  // 最多支持100个订单关联
static OrderSymbolPair g_order_symbol_pairs[MAX_ORDER_PAIRS];
static size_t g_order_pair_count = 0;

// 全局map，保存每个股票代码对应的最新tick数据
map<string, SecurityTickData> g_latest_ticks;
// 事先分配 - 涨停下单执行情况

string log_config = "0";
double limit_up_price_allo = 0.0;
struct timeval tv;
// 市场类型枚举
enum MarketType {
    SH = 0,  // 上海市场
    SZ = 1   // 深圳市场
};

// 封单检测订阅信息（支持SH/SZ双市场）
struct LimitUpSubscription {
    char symbol[12];            // 股票代码，使用char[12]避免动态分配
    MarketType market_type;     // 市场类型
    int64_t threshold_volume;   // 封单阈值（股数，SH直接比较；SZ用于计算金额）
    bool is_active;             // 是否激活
    uint64_t limit_up_price;    // 涨停价（用于快速比较）

    LimitUpSubscription() : market_type(SH), threshold_volume(0), is_active(false), limit_up_price(0) {
        memset(symbol, 0, 12);
    }
};


// SZ市场专用的实时数据
struct SZRealTimeData {
    int64_t base_ask_volume;        // 基准总卖量（来自tick的total_ask_vol）
    int64_t consumed_buy_volume;    // 3秒内消耗的涨停价买单量
    int64_t accumulated_volume;     // 3秒内累积的涨停价委托量
    int64_t last_reset_time;        // 上次重置时间

    // 新增：封单相关字段
    int64_t initial_seal_volume;    // 初始封单量（ask1_vol）
    int64_t current_seal_volume;    // 当前封单量
    int64_t max_sell_volume_15;     // 15s内的最大封单量
    int64_t seal_amount;            // 封单金额
    bool is_limit_up;               // 是否涨停状态

    // 计算剩余卖量
    int64_t GetRemainingAskVolume() const {
        return base_ask_volume - consumed_buy_volume;
    }
};

// 高性能订阅管理器 - 使用数组替代unordered_map
constexpr size_t MAX_SUBSCRIPTIONS = 32;  // 最多支持32只股票
static LimitUpSubscription g_limitup_subscriptions[MAX_SUBSCRIPTIONS];
static SZRealTimeData g_sz_realtime_data[MAX_SUBSCRIPTIONS];
static size_t g_subscription_count = 0;

// 高性能股票代码比较函数
inline bool is_symbol_equal(const char symbol1[12], const char symbol2[12]) {
    // 使用64位+32位比较，覆盖全部12字节，延时约3ns
    const uint64_t* sym1_64 = reinterpret_cast<const uint64_t*>(symbol1);
    const uint64_t* sym2_64 = reinterpret_cast<const uint64_t*>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t* sym1_32 = reinterpret_cast<const uint32_t*>(symbol1 + 8);
    const uint32_t* sym2_32 = reinterpret_cast<const uint32_t*>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}

// 查找订阅索引（热路径优化）
inline int find_subscription_index(const char symbol[12]) {
    for (size_t i = 0; i < g_subscription_count; ++i) {
        if (g_limitup_subscriptions[i].is_active &&
            is_symbol_equal(g_limitup_subscriptions[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;  // 未找到
}

inline int find_order_index(const char symbol[12]) {
    for (size_t i = 0; i < g_order_pair_count; ++i) {
        if (g_order_symbol_pairs[i].is_active &&
            is_symbol_equal(g_order_symbol_pairs[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;  // 未找到
}

// 获取指定股票的最新tick数据
SecurityTickData* GetLatestTick(const string& symbol) {
    auto it = g_latest_ticks.find(symbol);
    if (it != g_latest_ticks.end()) {
        return &(it->second);
    }
    return nullptr;  // 未找到该股票的tick数据
}

// 检查是否有指定股票的tick数据
bool HasTickData(const string& symbol) {
    return g_latest_ticks.find(symbol) != g_latest_ticks.end();
}

// 获取当前缓存的股票数量
size_t GetTickDataCount() {
    return g_latest_ticks.size();
}

// 识别市场类型 - 优化为char[12]版本
MarketType GetMarketType(const char symbol[12]) {
    // 高性能前缀比较，避免string构造
    if (symbol[0] == 'S' && symbol[1] == 'H' && symbol[2] == '.') {
        return SH;  // 上海市场
    } else if (symbol[0] == 'S' && symbol[1] == 'Z' && symbol[2] == '.') {
        return SZ;  // 深圳市场
    }
    return SH;  // 默认上海市场
}



// 订阅涨停板封单检测（支持SH/SZ双市场）- 优化为char[12]版本
void SubscribeLimitUpDetection(const char symbol[12], int64_t threshold_volume) {
    // 检查是否已达到最大订阅数
    if (g_subscription_count >= MAX_SUBSCRIPTIONS) {
        return;  // 订阅表满
    }

    // 检查是否已存在
    int existing_index = find_subscription_index(symbol);
    if (existing_index >= 0) {
        // 重新激活现有订阅
        g_limitup_subscriptions[existing_index].is_active = true;
        g_limitup_subscriptions[existing_index].threshold_volume = threshold_volume;
        return;
    }

    // 识别市场类型
    MarketType market_type = GetMarketType(symbol);

    // 创建新订阅
    LimitUpSubscription& subscription = g_limitup_subscriptions[g_subscription_count];
    memcpy(subscription.symbol, symbol, 12);
    subscription.market_type = market_type;
    subscription.threshold_volume = threshold_volume;
    subscription.is_active = true;
    subscription.limit_up_price = 0;  // 将在tick更新时设置

    // 初始化对应的实时数据
    SZRealTimeData& realtime_data = g_sz_realtime_data[g_subscription_count];
    memset(&realtime_data, 0, sizeof(SZRealTimeData));

    if (market_type == SH) {
        realtime_data.accumulated_volume = 0;
        realtime_data.last_reset_time = 0;
        realtime_data.base_ask_volume = 0;
        realtime_data.consumed_buy_volume = 0;
    } else if (market_type == SZ) {
        realtime_data.base_ask_volume = 0;
        realtime_data.consumed_buy_volume = 0;
        realtime_data.last_reset_time = 0;
        realtime_data.accumulated_volume = 0;
    }

    // 初始化封单相关字段
    realtime_data.initial_seal_volume = 0;
    realtime_data.current_seal_volume = 0;
    realtime_data.max_sell_volume_15 = 0;
    realtime_data.seal_amount = 0;
    realtime_data.is_limit_up = false;

    if (threshold_volume <= 0) {
        threshold_volume = 20000000;

    }

   // 增加订阅计数
    g_subscription_count++;

    ostringstream log;
    log << "已订阅涨停板封单检测: " << symbol
        << ", 市场: " << (market_type == SH ? "SH(简化)" : "SZ(复杂)")
        << ", 封单阈值: " << threshold_volume << " 股";
    strategy_log(StrategyLogLevel_Info, log.str().data());

}

// 取消涨停板封单检测订阅 - 优化为char[12]版本
bool UnsubscribeLimitUpDetection(const char symbol[12]) {
    // 查找订阅索引
    int index = find_subscription_index(symbol);
    if (index < 0) {
        ostringstream log;
        log << "取消订阅失败: 未找到股票 " << symbol << " 的订阅";
        strategy_log(StrategyLogLevel_Info, log.str().data());
        return false;
    }

    // 标记为非激活状态（不删除，避免数组重排）
    g_limitup_subscriptions[index].is_active = false;

    ostringstream log;
    log << "成功取消订阅: " << symbol;
    strategy_log(StrategyLogLevel_Info, log.str().data());

    return true;
}

// 检查是否已订阅指定股票 - 优化为char[12]版本
bool IsSubscribed(const char symbol[12]) {
    return find_subscription_index(symbol) >= 0;
}



// 保存订单号和股票代码关联（预分配内存，无动态分配）
void SaveOrderSymbolPair(const char* order_id, const char symbol[12], int64_t data_time = 0) {
    if (g_order_pair_count >= MAX_ORDER_PAIRS) {
        ostringstream log;
        log << "订单关联数组已满，无法保存新订单: " << order_id;
        strategy_log(StrategyLogLevel_Error, log.str().data());
        return;
    }

    OrderSymbolPair& pair = g_order_symbol_pairs[g_order_pair_count];

    strncpy(pair.order_id, order_id, sizeof(pair.order_id) - 1);
    memcpy(pair.symbol, symbol, 12);
    pair.is_active = true;
    pair.data_time = data_time;  // 保存触发下单的逐笔数据时间

    g_order_pair_count++;

    ostringstream log;
    log << "保存订单关联: 订单号=" << order_id << ", 股票=" << symbol
        << ", 逐笔数据时间=" << data_time
        << ", 索引=" << (g_order_pair_count - 1);
    strategy_log(StrategyLogLevel_Info, log.str().data());
}

// 根据订单号查找股票代码（线性查找，热路径优化）
const char* FindSymbolByOrderId(const char* order_id) {
    for (size_t i = 0; i < g_order_pair_count; ++i) {
        if (g_order_symbol_pairs[i].is_active &&
            strcmp(g_order_symbol_pairs[i].order_id, order_id) == 0) {
            return g_order_symbol_pairs[i].symbol;
        }
    }
    return nullptr;
}


// SZ市场专用：重置实时数据（每个tick周期调用）- 优化为char[12]版本
void ResetSZRealTimeData(const char symbol[12], int64_t current_time, int64_t total_ask_vol) {
    int index = find_subscription_index(symbol);
    if (index >= 0) {
        SZRealTimeData& data = g_sz_realtime_data[index];
        // 重置消耗的买单量，更新基准卖量
        data.consumed_buy_volume = 0;
        data.last_reset_time = current_time;
        data.base_ask_volume = total_ask_vol;

        ostringstream log;
        log << "SZ重置实时数据: " << symbol
            << ", 基准卖量: " << total_ask_vol << " 股";
        strategy_log(StrategyLogLevel_Debug, log.str().data());
    }
}


// 涨停板下单函数 - 按金额下单，优化为char[12]版本
int OrderLimitUp(const char symbol[12], double amount, int64_t limit_up_price, int64_t data_time = 0) {
  
    // 检查涨停价是否有效
    if (limit_up_price <= 0) {
        ostringstream log;
        log << "涨停板下单失败: " << symbol << " 涨停价无效";
        strategy_log(StrategyLogLevel_Error, log.str().data());
        return -2;
    }

    // 根据金额和涨停价计算股数（100股的整数倍）
    double price_yuan = limit_up_price / 10000.0;  // 转换为元
    int volume = (int)(amount / price_yuan / 100) * 100;  // 向下取整到100股

    if (volume < 100) {
        ostringstream log;
        log << "涨停板下单失败: " << symbol
            << " 金额 " << amount << " 元不足购买100股"
            << " (涨停价: " << price_yuan << " 元)";
        strategy_log(StrategyLogLevel_Error, log.str().data());
        return -3;
    }

    // 构造订单
    OrderReq req;
    memset(&req, 0x00, sizeof(OrderReq));
    memcpy(req.symbol, symbol, 12);  // 直接复制char[12]
    req.order_type = OrderType_LMT;  // 限价单
    req.side = OrderSide::OrderSide_Bid;  // 买入
    req.volume = 100;
    req.price = limit_up_price;  // 使用涨停价
    req.hedge_flag = HedgeFlag::HedgeFlag_Placeholder;

    // 执行下单
    int rc = td_order(kStockAccount, AccountType_Stock, &req, 1);

    ostringstream log;
    if (rc == 0) {
        double actual_amount = volume * price_yuan;  // 实际使用金额
        log << "涨停板下单成功: " << symbol
            << ", 订单号: " << req.order_id
            << ", 涨停价: " << price_yuan << " 元"
            << ", 数量: " << volume << " 股"
            << ", 预期金额: " << amount << " 元"
            << ", 实际金额: " << actual_amount << " 元"
            << ", 逐笔数据时间: " << data_time;
        strategy_log(StrategyLogLevel_Info, log.str().data());

        // 保存订单号和股票代码关联，同时保存触发下单的逐笔数据时间
        SaveOrderSymbolPair(req.order_id, symbol, data_time);

    } else {
        log << "涨停板下单失败: " << symbol
            << ", 错误码: " << rc
            << ", 错误信息: " << hft_strerror_utf8(rc)
            << ", req.ordertype: " << req.order_type
            << ", req.side: " << req.side
            << ", req.volume: " << req.volume
            << ", req.price: " << req.price
            << ", req.hedge_flag: " << req.hedge_flag
            << ", req.symbol: " << req.symbol;
        strategy_log(StrategyLogLevel_Error, log.str().data());
    }

    return rc;
}


// SH市场封单检测（累积版）- 累积多笔涨停价委托，优化为char[12]版本
bool CheckAndExecuteSHLimitUpSeal(const char symbol[12], int64_t limit_up_volume, int64_t threshold_amount, int64_t time) {
    int index = find_subscription_index(symbol);
    if (index < 0) {
        return false;
    }

    return false;
}





// 返回当前日期时间，格式为YYYYMMDDhhmmss
static uint64_t datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return err > 0 ? -err : err;
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return errno > 0 ? -errno : errno;
    }
#endif

    uint64_t ndate = (tmval.tm_year + 1900) * 10000 + (tmval.tm_mon + 1) * 100 +
                     tmval.tm_mday;
    uint64_t ntime = tmval.tm_hour * 10000 + tmval.tm_min * 100 + tmval.tm_sec;
    return ndate * 1000000 + ntime;
}

// 返回当前日期时间，格式为YYYY-MM-DD hh:mm:ss
static string str_datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return "";
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return "";
    }
#endif

    char szTemp[32];
    snprintf(szTemp, sizeof(szTemp), "%04d-%02d-%02d %02d:%02d:%02d",
             tmval.tm_year + 1900, tmval.tm_mon + 1, tmval.tm_mday,
             tmval.tm_hour, tmval.tm_min, tmval.tm_sec);
    return szTemp;
}


// 证券Tick行情回调处理函数 - 优化为char[12]版本
void OnSecurityTick(SecurityTickData* res, void* user_data) {
    return;
}

// 证券K线行情回调处理函数
void OnSecurityKdata(SecurityKdata* res, void* user_data) {
    // 根据K线行情，使用这一分钟开盘价下单

    ostringstream oss;
    oss << "OnSecurityKdata: res: " << res->time << "  " << res->symbol << " " << res->high;

    strategy_log(StrategyLogLevel_Info, oss.str().data());

    return;
}

// 静态变量用于统计
static int data_count = 0;
static const int SAMPLE_SIZE = 1000;  // 每1000个数据计算一次延迟
void OnTickbytickData(TickByTickData* tbt, void* user_data) {
    // 高性能热路径：直接使用char[12]，避免string构造
    
    
    // 先增加计数
    data_count++;
    
    // 只在计数是1000的倍数时计算延迟
    if (data_count % SAMPLE_SIZE == 0) {
        // 获取当前系统时间
        
        gettimeofday(&tv, NULL);
        long long now_ms = tv.tv_usec / 1000;

        // 计算延迟（毫秒级）
        int64_t latency = (now_ms - (tbt->data_time%1000));
        
        // 判断市场类型
        MarketType market_type = GetMarketType(tbt->symbol);
        
        ostringstream log;
        if (market_type == SH) {
            // 上海市场
            log << "【延迟统计-SH】样本计数: " << data_count 
                << ", 当前延迟: " << latency << " 毫秒"
                << ", 当前系统时间: "  << now_ms
                << ", 最新L2数据时间: " << tbt->data_time;
        } else if (market_type == SZ) {
            // 深圳市场
            log << "【延迟统计-SZ】样本计数: " << data_count 
                << ", 当前延迟: " << latency << " 毫秒"
                << ", 当前系统时间: " << now_ms
                << ", 最新L2数据时间: " << tbt->data_time;
        }
        
        strategy_log(StrategyLogLevel_Info, log.str().data());
    
	}
}

void OnIndexTickCallback(IndexTickData* res, void* user_data) {
    //处理指数行情  
}

// 市场日期变更回调处理函数
void OnDateUpdate(DateUpdateData* dud, void* user_data) {
    ostringstream oss;
    oss << "OnDateUpdate: market: " << dud->market << ", date: " << dud->date;

    strategy_log(StrategyLogLevel_Info, oss.str().data());
    std::cout << "OnDateUpdate: market: " << dud->market
              << ", date: " << dud->date << endl;
}

// 2. 创建JSON对象示例
//json strategy_config;
//strategy_config["strategy_name"] = "LimitUpStrategy";
//strategy_config["version"] = "1.0.0";
//strategy_config["author"] = "Quant Team";
//
//// 添加订阅配置
//json subscriptions = json::array();
//for (const auto& pair : g_limitup_subscriptions) {
//    json sub_info;
//    sub_info["symbol"] = pair.first;
//    sub_info["threshold"] = pair.second.threshold_volume;
//    sub_info["market"] = (pair.second.market_type == SH) ? "SH" : "SZ";
//    sub_info["active"] = pair.second.is_active;
//    subscriptions.push_back(sub_info);
//}
//strategy_config["subscriptions"] = subscriptions;
//
//// 添加统计信息
//strategy_config["stats"]["total_subscriptions"] = g_limitup_subscriptions.size();
//strategy_config["stats"]["tick_data_count"] = g_latest_ticks.size();
//strategy_config["stats"]["current_time"] = str_datetime_now();
//
//ostringstream config_log;
//config_log << "=== 策略配置JSON ===" << endl;
//config_log << strategy_config.dump(4);
//strategy_log(StrategyLogLevel_Info, config_log.str().data());
// 策略参数设置回调函数
void OnStrategyParamsSetting(const char *params_json, void *user_data) {

    // 获取当前系统时间
    int o_date = 0, o_time = 0;
    // 记录起始时间戳
    // 使用 __rdtsc 记录CPU周期

    auto start_cycles = __rdtsc();
    strategy_get_datetime(&o_date, &o_time);
    auto end_cycles = __rdtsc();

    ostringstream datetime_log;
    datetime_log << "strategy_get_datetime 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期" << o_time;
    strategy_log(StrategyLogLevel_Info, datetime_log.str().data());

    // 方法1: 系统时间函数
#ifdef _WIN32
    // Windows系统使用GetSystemTime和FileTime
    start_cycles = __rdtsc();
    SYSTEMTIME st;
    GetSystemTime(&st);
    FILETIME ft;
    SystemTimeToFileTime(&st, &ft);
    ULARGE_INTEGER uli;
    uli.LowPart = ft.dwLowDateTime;
    uli.HighPart = ft.dwHighDateTime;
    long long now_ms_win = (uli.QuadPart - 116444736000000000ULL) / 10000; // 转换为毫秒
    end_cycles = __rdtsc();
    
    ostringstream ms_log_win;
    ms_log_win << "方法1 (Windows GetSystemTime): 当前时间戳(毫秒): " << now_ms_win
              << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, ms_log_win.str().data());
#else
    // Linux/Unix系统使用gettimeofday
    start_cycles = __rdtsc();
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long long now_ms = tv.tv_usec / 1000;
    end_cycles = __rdtsc();
    
    ostringstream ms_log;
    ms_log << "方法1 (gettimeofday): 当前时间戳(毫秒): " << (now_ms) 
           << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, ms_log.str().data());
#endif

    // 方法2: std::chrono::system_clock (C++11标准库)
    start_cycles = __rdtsc();
    auto now = std::chrono::system_clock::now();
    auto now_ms_chrono = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()).count();
    end_cycles = __rdtsc();
    
    ostringstream chrono_log;
    chrono_log << "方法2 (std::chrono::system_clock): 当前时间戳(毫秒): " << (now_ms_chrono) 
              << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, chrono_log.str().data());

    // 方法3: std::chrono::high_resolution_clock (C++11高精度时钟)
    start_cycles = __rdtsc();
    auto now_high_res = std::chrono::high_resolution_clock::now();
    auto now_ms_high_res = std::chrono::duration_cast<std::chrono::milliseconds>(
        now_high_res.time_since_epoch()).count();
    end_cycles = __rdtsc();
    
    ostringstream high_res_log;
    high_res_log << "方法3 (std::chrono::high_resolution_clock): 当前时间戳(毫秒): " << (now_ms_high_res) 
                << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, high_res_log.str().data());

    // 方法4: strategy_get_millseconds (SDK提供的毫秒级时间函数)
    start_cycles = __rdtsc();
    int64_t sdk_ms = strategy_get_millseconds();
    end_cycles = __rdtsc();
    
    ostringstream sdk_log;
    sdk_log << "方法4 (strategy_get_millseconds): 当前时间戳(毫秒): " << (sdk_ms) 
           << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, sdk_log.str().data());

#ifdef _WIN32
    // 方法5: Windows QueryPerformanceCounter (高精度计数器)
    start_cycles = __rdtsc();
    LARGE_INTEGER freq, count;
    QueryPerformanceFrequency(&freq);
    QueryPerformanceCounter(&count);
    long long qpc_ms = (count.QuadPart * 1000) / freq.QuadPart;
    end_cycles = __rdtsc();
    
    ostringstream qpc_log;
    qpc_log << "方法5 (QueryPerformanceCounter): 高精度计数(毫秒): " << (qpc_ms) 
           << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, qpc_log.str().data());
#else
    // 方法5: clock_gettime (POSIX高精度时钟)
    start_cycles = __rdtsc();
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    long long clock_ms = ts.tv_nsec / 1000000;
    end_cycles = __rdtsc();
    
    ostringstream clock_log;
    clock_log << "方法5 (clock_gettime): 当前时间戳(毫秒): " << clock_ms 
             << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, clock_log.str().data());
#endif

    // 方法6: 使用__rdtsc本身估算时间
    // 注意：这需要预先知道CPU频率，且在现代CPU上可能不准确
    static double cpu_freq_mhz = 0.0;
    if (cpu_freq_mhz == 0.0) {
        // 使用系统时间估算CPU频率
        start_cycles = __rdtsc();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        end_cycles = __rdtsc();
        cpu_freq_mhz = (end_cycles - start_cycles) / 100000.0; // 100ms转换为MHz
    }
    
    start_cycles = __rdtsc();
    unsigned long long tsc_value = __rdtsc();
    double tsc_ms = tsc_value / (cpu_freq_mhz * 1000.0);
    end_cycles = __rdtsc();
    
    ostringstream tsc_log;
    tsc_log << "方法6 (__rdtsc估算): 估算时间(毫秒): " << (tsc_ms) 
           << ", CPU频率(MHz): " << cpu_freq_mhz
           << ", 执行耗时: " << (end_cycles - start_cycles) << " 个CPU周期";
    strategy_log(StrategyLogLevel_Info, tsc_log.str().data());

    // 性能比较总结
    ostringstream summary;
    summary << "==== 时间获取方法性能比较 ====" << std::endl
            << "注意: 较低的CPU周期数表示更高的性能" << std::endl
            << "最适合量化交易的方法通常是: " << std::endl
            << "1. __rdtsc (仅用于相对时间测量)" << std::endl
            << "2. strategy_get_millseconds (如果SDK实现高效)" << std::endl
            << "3. clock_gettime/QueryPerformanceCounter (系统高精度时钟)";
    strategy_log(StrategyLogLevel_Info, summary.str().data());
}

// 成交回报回调处理函数
void OnTradeReport(const Trade* trade, void* user_data) {
    // 输出成交回报到strategy目录的日志中
    ostringstream osstream;
    osstream << "OnTradeReport: " << endl
             << "\tstrategy_id: " << trade->strategy_id << endl
             << "\trun_id: " << trade->run_id << endl
             << "\torder_id: " << trade->order_id << endl
             << "\tcl_order_id: " << trade->cl_order_id << endl
             << "\tsymbol: " << trade->symbol << endl
             << "\taccount_id: " << trade->account_id << endl
             << "\taccount_type: " << trade->account_type << endl
             << "\tdate: " << trade->date << endl
             << "\ttrade_seqno: " << trade->trade_seqno << endl
             << "\tside: " << trade->side << endl
             << "\torder_type: " << trade->order_type << endl
             << "\texec_type: " << trade->exec_type << endl
              << "\texec_id: " << trade->exec_id << endl
             << "\tvolume: " << trade->volume << endl
             << "\tprice: " << trade->price << endl
             << "\tturnover: " << trade->turnover << endl
             << "\ttransact_time: " << trade->transact_time;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 委托应答回调处理函数
void OnOrderRsp(const OrderRsp* order_rsp, int count, void* user_data) {
    // 输出委托应答到strategy目录的日志中
    ostringstream osstream;
    for (int i = 0; i < count; ++i) {
        osstream.str("");
        osstream << "OrderRsp: " << endl
                 << "\torder_id: " << order_rsp[i].order_id << endl
                 << "\tcl_order_id: " << order_rsp[i].cl_order_id << endl
                 << "\terr_code: " << order_rsp[i].err_code << endl
                 << "\terr_msg: " << order_rsp[i].err_msg;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }
}

// 委托状态回调处理函数
void OnOrderStatus(const Order* res, void* user_data) {
    // 输出委托状态到strategy目录的日志中
    ostringstream osstream;
    osstream << "OnOrderStatus: " << endl
             << "\tstrategy_id: " << res->strategy_id << endl
             << "\trun_id: " << res->run_id << endl
             << "\torder_id: " << res->order_id << endl
             << "\tcl_order_id: " << res->cl_order_id << endl
             << "\tsymbol: " << res->symbol << endl
             << "\taccount_id: " << res->account_id << endl
             << "\taccount_type: " << res->account_type << endl
             << "\tprice: " << res->price << endl
             << "\tmarketdata_time: " << res->marketdata_time << endl
             << "\tcreate_time: " << res->create_time << endl
             << "\tupdate_time: " << res->update_time << endl
             << "\tside: " << res->side << endl
             << "\torder_status: " << res->order_status << endl
             << "\tfilled_volume: " << res->filled_volume << endl
             << "\tfilled_turnover: " << res->filled_turnover;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 行情服务状态回调
void OnMdStatusChange(int conn_status, void* user_data) {
    ostringstream osstream;
    osstream << "OnMdStatusChange: " << conn_status;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 定时回调处理函数
void OnStrategyTimer(int interval, void* user_data) {
    ostringstream osstream;

    // 定时任务中只调用一次get_security_ticks，作为示例
    static bool security_ticks_called = false;
    if (!security_ticks_called) {
        security_ticks_called = true;
        SecurityTickData* sd = NULL;
        int count = 0;
        int ret = get_security_ticks("SH.600000,SZ.000001", "2020-1-2 10:0:0",
                                     "2020-1-2 16:0:0", &sd, &count);

        osstream.str("");
        osstream << "get_security_ticks: " << ret << ", count:" << count
                 << ", sd: " << sd;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());

       
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }

    // 定时任务中只调用一次get_security_kdata，作为示例
    static bool security_kdata_called = false;
    if (!security_kdata_called) {
        security_kdata_called = true;
        SecurityKdata* sd = NULL;
        int count = 0;
        int ret = get_security_kdata("SH.600000", "2018/6/1", "2018/6/1",
                                     "1min", "none", &sd, &count);

        osstream.str("");
        osstream << "get_security_kdata: " << ret << ", count:" << count
                 << ", sd: " << sd;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }

    if (interval == 5000) {
        // 每5秒钟更新一次参数和自定义指标
        char szJsonContent[256];
        snprintf(szJsonContent, sizeof(szJsonContent),
                "[{\"key\":\"DateTime\", \"name\":\"DateTime\", "
                "\"type\":\"datetime\", \"value\":\"%s\"}, "
                "{\"key\":\"OrderNum\", \"name\":\"OrderNum\", \"type\":\"int\", \"value\":%zu}"

                "]"
                 ,str_datetime_now().data(), g_orderids.size());
        strategy_report_indexes(szJsonContent);
    }
}

void OnDayTaskCallback(int time, void* user_data) {
    ostringstream osstream;
    std::cout << "OnDayTaskCallback: " << time << std::endl;

    orderCount = 0;  //置0，让当天又可以下单交易
    if (g_orderids.size() > 0)
        g_orderids.clear();

    osstream << "OnDayTaskCallback: " << time;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

void OnExit(int reason, void* user_data) {
    // 退出回调做一些资源回收等最终操作
    ostringstream osstream;
    osstream << "OnExit, reason: " << strategy_exit_reason(reason);
    const string& message = osstream.str();
    cout << message << endl;
    strategy_log(StrategyLogLevel_Info, message.data());
    std::cout << "OnExit, reason: " << strategy_exit_reason(reason);
}

#ifdef _WIN32
bool CtrlHandler(DWORD fdwctrltype) {
    switch (fdwctrltype) {
            // handle the ctrl-c signal.
        case CTRL_C_EVENT:
            strategy_exit();
            return (true);

        default:
            return false;
    }
}
#endif


int main(int argc, const char* argv[]) {
// 处理命令行CTRL-C退出信号
#ifdef _WIN32
    if (!SetConsoleCtrlHandler((PHANDLER_ROUTINE)CtrlHandler, TRUE)) {
        cout << "ERROR: Could not set console control handler" << endl;
        strategy_log(StrategyLogLevel_Error,
                     "ERROR: Could not set console control handler");
        return 1;
    }
#endif
        // 初始化策略
    ostringstream osstream;
    int rc = strategy_init();
    int mode = 0;

    if (0 != rc) {
        osstream << "strategy_init failed: " << rc
                 << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_init failed: " << rc
             << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
        goto OnProgramExit;
    }

    cout << "strategy_init success" << endl;
    strategy_log(StrategyLogLevel_Info, "strategy_init success");

    // 初始化封单检测（支持SH/SZ双市场）- 使用char[12]格式
    char symbol1[12], symbol2[12], symbol3[12], symbol4[12];

    // 设置交易相关回调事件处理函数
    td_set_trade_report_callback(OnTradeReport, NULL);
    td_set_order_rsp_callback(OnOrderRsp, NULL);
    td_set_order_status_callback(OnOrderStatus, NULL);

    // 设置实时行情回调事件处理函数
    md_set_status_change_callback(OnMdStatusChange, NULL);

    // 设置行情回调函数
    md_set_security_tick_callback(OnSecurityTick, NULL);  //证券tick行情回调
    md_set_index_tick_callback(OnIndexTickCallback, NULL);
    md_set_security_kdata_callback(OnSecurityKdata, NULL);  //证券分钟K线回调
    md_set_tickbytick_callback(OnTickbytickData, NULL);
    // md_set_orderqueue_callback(OnOrderQueue, NULL);  // 委托队列数据回调

    md_set_date_update_callback(OnDateUpdate, NULL);  //日期变化回调

    // 设置定时任务回调处理函数
    strategy_set_timer_callback(OnStrategyTimer, NULL);

    // 设置策略退出回调处理函数
    strategy_set_exit_callback(OnExit, NULL);

    // 设置一个5s定时任务
    strategy_set_timer(5000);

    strategy_set_day_schedule_task_callback(OnDayTaskCallback, NULL);
    strategy_set_day_schedule_task(85000);

    // 订阅一个Tick，多个K线行情
    // md_subscribe("SH.601211.tick,SH.601211.bar,SH.000001.index");

    strategy_set_params_setting_callback(OnStrategyParamsSetting, NULL);

    
    char myparam[256];
    snprintf(myparam, sizeof(myparam),
        "["
        "{\"key\":\"Code\", \"name\":\"Code\", \"type\":\"string\", \"value\":\"SH.600002\"},"
        "{\"key\":\"Log\", \"name\":\"LOg\", \"type\":\"string\", \"value\":\"0\"},"

        "{\"key\":\"DCode\", \"name\":\"DCode\", \"type\":\"string\", \"value\":\"SH.\"}"

        "]");
    strategy_report_params(myparam);


    // 运行策略
    // mode 0 - 默认模式, 事件触发
    // 1 - spin模式，通过死循环检测事件队列中是否有新的事件到达。
    rc = strategy_run(1);
    if (rc != 0) {
        osstream.str("");
        osstream << "strategy_run failed: " << rc
                 << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_run failed: " << rc
             << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
    } else {
        cout << "strategy_run success" << endl;
        strategy_log(StrategyLogLevel_Info, "strategy_run success");
    }

    // 主线程等待策略线程退出
    while (strategy_get_exec_status() != StrategyExecStatus_Term) {
        // sleep 500ms
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

OnProgramExit:
    if (strategy_get_exec_status() != StrategyExecStatus_Term) {
        rc = strategy_exit();
        if (rc != 0) {
            osstream.str("");
            osstream << "strategy_exit failed: " << rc
                     << ", msg: " << hft_strerror_utf8(rc);
            cout << "strategy_exit failed: " << rc
                 << ", msg: " << hft_strerror(rc) << endl;
            strategy_log(StrategyLogLevel_Info, osstream.str().data());
        } else {
            cout << "strategy_exit success" << endl;
            strategy_log(StrategyLogLevel_Info, "strategy_exit success");
        }
    }
    return 0;
}

