# 300ms滑动窗口性能测试
# 测试场景：高频交易场景下的性能表现
# 目标：验证O(1)增量计算相比O(40)全量计算的性能提升

# 1. 初始tick数据 - 确保股票就绪
[93000000]|SH.600002|TICK|V:1000000|T:93000000|M:107000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000
[93000000]|SZ.000001|TICK|V:800000|T:93000000|M:107000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000

# 1.1 添加更多tick数据确保股票就绪
[93000005]|SH.600002|TICK|V:1000000|T:93000005|M:108000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000

# 2. 高频交易数据：每10ms一笔交易，持续1秒（100笔交易）
# 这将测试增量计算的性能优势

# 第1组：0-100ms，每10ms一笔
[93000010]|SH.600002|BT|V:1000000|T:93000010|P:110000|TF:B|BN:1001|AN:2001
[93000020]|SH.600002|BT|V:1000000|T:93000020|P:110000|TF:B|BN:1002|AN:2002
[93000030]|SH.600002|BT|V:1000000|T:93000030|P:110000|TF:B|BN:1003|AN:2003
[93000040]|SH.600002|BT|V:1000000|T:93000040|P:110000|TF:B|BN:1004|AN:2004
[93000050]|SH.600002|BT|V:1000000|T:93000050|P:110000|TF:B|BN:1005|AN:2005
[93000060]|SH.600002|BT|V:1000000|T:93000060|P:110000|TF:B|BN:1006|AN:2006
[93000070]|SH.600002|BT|V:1000000|T:93000070|P:110000|TF:B|BN:1007|AN:2007
[93000080]|SH.600002|BT|V:1000000|T:93000080|P:110000|TF:B|BN:1008|AN:2008
[93000090]|SH.600002|BT|V:1000000|T:93000090|P:110000|TF:B|BN:1009|AN:2009
[93000100]|SH.600002|BT|V:1000000|T:93000100|P:110000|TF:B|BN:1010|AN:2010

# 第2组：100-200ms
[93000110]|SH.600002|BT|V:1000000|T:93000110|P:110000|TF:B|BN:1011|AN:2011
[93000120]|SH.600002|BT|V:1000000|T:93000120|P:110000|TF:B|BN:1012|AN:2012
[93000130]|SH.600002|BT|V:1000000|T:93000130|P:110000|TF:B|BN:1013|AN:2013
[93000140]|SH.600002|BT|V:1000000|T:93000140|P:110000|TF:B|BN:1014|AN:2014
[93000150]|SH.600002|BT|V:1000000|T:93000150|P:110000|TF:B|BN:1015|AN:2015
[93000160]|SH.600002|BT|V:1000000|T:93000160|P:110000|TF:B|BN:1016|AN:2016
[93000170]|SH.600002|BT|V:1000000|T:93000170|P:110000|TF:B|BN:1017|AN:2017
[93000180]|SH.600002|BT|V:1000000|T:93000180|P:110000|TF:B|BN:1018|AN:2018
[93000190]|SH.600002|BT|V:1000000|T:93000190|P:110000|TF:B|BN:1019|AN:2019
[93000200]|SH.600002|BT|V:1000000|T:93000200|P:110000|TF:B|BN:1020|AN:2020

# 第3组：200-300ms
[93000210]|SH.600002|BT|V:1000000|T:93000210|P:110000|TF:B|BN:1021|AN:2021
[93000220]|SH.600002|BT|V:1000000|T:93000220|P:110000|TF:B|BN:1022|AN:2022
[93000230]|SH.600002|BT|V:1000000|T:93000230|P:110000|TF:B|BN:1023|AN:2023
[93000240]|SH.600002|BT|V:1000000|T:93000240|P:110000|TF:B|BN:1024|AN:2024
[93000250]|SH.600002|BT|V:1000000|T:93000250|P:110000|TF:B|BN:1025|AN:2025
[93000260]|SH.600002|BT|V:1000000|T:93000260|P:110000|TF:B|BN:1026|AN:2026
[93000270]|SH.600002|BT|V:1000000|T:93000270|P:110000|TF:B|BN:1027|AN:2027
[93000280]|SH.600002|BT|V:1000000|T:93000280|P:110000|TF:B|BN:1028|AN:2028
[93000290]|SH.600002|BT|V:1000000|T:93000290|P:110000|TF:B|BN:1029|AN:2029
[93000300]|SH.600002|BT|V:1000000|T:93000300|P:110000|TF:B|BN:1030|AN:2030

# 第4组：300-400ms（开始触发过期槽位移除）
[93000310]|SH.600002|BT|V:1000000|T:93000310|P:110000|TF:B|BN:1031|AN:2031
[93000320]|SH.600002|BT|V:1000000|T:93000320|P:110000|TF:B|BN:1032|AN:2032
[93000330]|SH.600002|BT|V:1000000|T:93000330|P:110000|TF:B|BN:1033|AN:2033
[93000340]|SH.600002|BT|V:1000000|T:93000340|P:110000|TF:B|BN:1034|AN:2034
[93000350]|SH.600002|BT|V:1000000|T:93000350|P:110000|TF:B|BN:1035|AN:2035
[93000360]|SH.600002|BT|V:1000000|T:93000360|P:110000|TF:B|BN:1036|AN:2036
[93000370]|SH.600002|BT|V:1000000|T:93000370|P:110000|TF:B|BN:1037|AN:2037
[93000380]|SH.600002|BT|V:1000000|T:93000380|P:110000|TF:B|BN:1038|AN:2038
[93000390]|SH.600002|BT|V:1000000|T:93000390|P:110000|TF:B|BN:1039|AN:2039
[93000400]|SH.600002|BT|V:1000000|T:93000400|P:110000|TF:B|BN:1040|AN:2040

# 在300ms时刻应该触发下单（累积30笔 = 3000万股）
# 在310ms时刻，10ms的槽位应该被移除，但总量仍然是3000万股

# 第5组：继续到500ms，测试持续的增量更新
[93000410]|SH.600002|BT|V:1000000|T:93000410|P:110000|TF:B|BN:1041|AN:2041
[93000420]|SH.600002|BT|V:1000000|T:93000420|P:110000|TF:B|BN:1042|AN:2042
[93000430]|SH.600002|BT|V:1000000|T:93000430|P:110000|TF:B|BN:1043|AN:2043
[93000440]|SH.600002|BT|V:1000000|T:93000440|P:110000|TF:B|BN:1044|AN:2044
[93000450]|SH.600002|BT|V:1000000|T:93000450|P:110000|TF:B|BN:1045|AN:2045
[93000460]|SH.600002|BT|V:1000000|T:93000460|P:110000|TF:B|BN:1046|AN:2046
[93000470]|SH.600002|BT|V:1000000|T:93000470|P:110000|TF:B|BN:1047|AN:2047
[93000480]|SH.600002|BT|V:1000000|T:93000480|P:110000|TF:B|BN:1048|AN:2048
[93000490]|SH.600002|BT|V:1000000|T:93000490|P:110000|TF:B|BN:1049|AN:2049
[93000500]|SH.600002|BT|V:1000000|T:93000500|P:110000|TF:B|BN:1050|AN:2050

# 在这个测试中，我们期望：
# 1. 在300ms时刻触发第一次下单（3000万股）
# 2. 系统能够高效处理后续的增量更新
# 3. 每次添加新数据时，只需要O(1)操作而不是O(40)遍历
