# 不应该下单的测试场景
# 场景：验证300ms窗口提前下单逻辑的边界条件
# 测试逻辑：
# 1. 累积量不足2000万 - 不应该下单
# 2. 剩余封单充足（超过200万）- 不应该下单  
# 3. 价格不匹配涨停价 - 不应该下单
# 4. 超过300ms窗口重置 - 不应该下单

# 1. 初始tick数据 - 设置基准卖量和涨停价
# TAV=3000000 表示300万股卖量，剩余封单金额=300万*11万/万=3300万元 > 200万元（不触发）
[93000100]|SH.600002|TICK|V:1000000|T:93000100|M:107000|TAV:3000000|BP1:110000|BV1:5000000|PC:100000
[93000200]|SZ.000001|TICK|V:800000|T:93000200|M:107000|TAV:3000000|BP1:110000|BV1:5000000|PC:100000

# 场景1：SH市场累积量不足2000万（只有1500万）- 不应该下单
[93000300]|SH.600002|BT|V:5000000|T:93000300|P:110000|TF:B|BN:1001|AN:2001
[93000350]|SH.600002|BT|V:6000000|T:93000350|P:110000|TF:B|BN:1002|AN:2002
[93000400]|SH.600002|BT|V:4000000|T:93000400|P:110000|TF:B|BN:1003|AN:2003
# 累积量：5000000 + 6000000 + 4000000 = 15000000 < 20000000，不应该下单

# 场景2：SZ市场剩余封单充足（超过200万元）- 不应该下单
[93000500]|SZ.000001|BW|V:8000000|T:93000500|P:110000|S:1|OT:A|N:3001
[93000550]|SZ.000001|BW|V:7000000|T:93000550|P:110000|S:1|OT:A|N:3002
[93000600]|SZ.000001|BW|V:6000000|T:93000600|P:110000|S:1|OT:A|N:3003
# 累积量：8000000 + 7000000 + 6000000 = 21000000 > 20000000 ✅
# 剩余封单：(3000000 - 21000000) = -18000000（负数，说明封单被吃完）
# 但是由于累积量不足300万*11=3300万元的大单，不会触发检测

# 场景3：价格不匹配涨停价 - 不应该下单
[93000700]|SH.600002|BT|V:8000000|T:93000700|P:109000|TF:B|BN:1004|AN:2004  # 价格109000 != 涨停价110000
[93000750]|SH.600002|BT|V:7000000|T:93000750|P:109000|TF:B|BN:1005|AN:2005  # 价格109000 != 涨停价110000
[93000800]|SH.600002|BT|V:6000000|T:93000800|P:109000|TF:B|BN:1006|AN:2006  # 价格109000 != 涨停价110000
# 虽然累积量超过2000万，但价格不匹配，不应该下单

# 场景4：300ms窗口重置测试 - 不应该下单
[93001000]|SZ.000001|BW|V:1000000|T:93001000|P:110000|S:1|OT:A|N:3004  # 距离上次超过300ms，窗口重置
[93001050]|SZ.000001|BW|V:800000|T:93001050|P:110000|S:1|OT:A|N:3005   # 新窗口内累积量不足
# 单笔金额：100万*11万/万=110万元 < 300万元，不会触发大单检测

# 场景5：卖方成交（不是买方成交）- 不应该下单
[93001200]|SH.600002|BT|V:1000000|T:93001200|P:110000|TF:S|BN:1007|AN:2007  # TF:S表示卖方成交
[93001250]|SH.600002|BT|V:800000|T:93001250|P:110000|TF:S|BN:1008|AN:2008   # TF:S表示卖方成交
[93001300]|SH.600002|BT|V:500000|T:93001300|P:110000|TF:S|BN:1009|AN:2009   # TF:S表示卖方成交
# 都是卖方成交，不会被累积到买方滑动窗口中，不应该下单

# 场景6：SZ市场卖方委托（不是买方委托）- 不应该下单
[93001400]|SZ.000001|BW|V:1000000|T:93001400|P:110000|S:2|OT:A|N:3006  # S:2表示卖方委托
[93001450]|SZ.000001|BW|V:800000|T:93001450|P:110000|S:2|OT:A|N:3007   # S:2表示卖方委托
[93001500]|SZ.000001|BW|V:500000|T:93001500|P:110000|S:2|OT:A|N:3008   # S:2表示卖方委托
# 都是卖方委托，不会被累积到买方滑动窗口中，不应该下单
