# 300ms滑动窗口算法单元测试
# 测试场景：验证滑动窗口能够捕获跨越固定窗口边界的交易量
# 关键测试点：
# 1. 固定窗口会遗漏的跨边界累积
# 2. 滑动窗口的正确计算
# 3. 时间槽的循环使用
# 4. 窗口重置机制

# 1. 初始tick数据 - 设置基准卖量和涨停价，确保is_ready被设置为true
[93000000]|SH.600002|TICK|V:1000000|T:93000000|M:107000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000
[93000000]|SZ.000001|TICK|V:800000|T:93000000|M:107000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000

# 1.1 添加更多tick数据确保股票就绪
[93000100]|SH.600002|TICK|V:1000000|T:93000100|M:108000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000
[93000100]|SZ.000001|TICK|V:800000|T:93000100|M:108000|TAV:25000000|BP1:110000|BV1:5000000|PC:100000

# 2. 测试场景1：跨越固定300ms边界的累积
# 时间轴: [0-300ms] [300-600ms]
# 如果用固定窗口，这些交易会被分到不同窗口，都不满足2000万条件

# 在150ms时刻：1500万股（固定窗口1）
[93000150]|SH.600002|BT|V:15000000|T:93000150|P:110000|TF:B|BN:1001|AN:2001

# 在450ms时刻：1500万股（固定窗口2）
[93000450]|SH.600002|BT|V:15000000|T:93000450|P:110000|TF:B|BN:1002|AN:2002

# 但是滑动窗口从150ms到450ms，总共3000万股，应该触发下单！

# 3. 测试场景2：SZ市场的类似情况
# 在200ms时刻：1200万股
[93000200]|SZ.000001|BW|V:12000000|T:93000200|P:110000|S:1|OT:A|N:3001

# 在400ms时刻：1000万股  
[93000400]|SZ.000001|BW|V:10000000|T:93000400|P:110000|S:1|OT:A|N:3002

# 滑动窗口从200ms到400ms，总共2200万股，应该触发下单！

# 4. 测试场景3：时间槽边界测试（10ms精度）
# 测试相同10ms时间槽内的累积
[93000500]|SH.600002|BT|V:8000000|T:93000500|P:110000|TF:B|BN:1003|AN:2003
[93000509]|SH.600002|BT|V:7000000|T:93000509|P:110000|TF:B|BN:1004|AN:2004
# 这两笔应该累积到同一个时间槽：1500万股

# 在700ms时刻再加500万股
[93000700]|SH.600002|BT|V:5000000|T:93000700|P:110000|TF:B|BN:1005|AN:2005
# 滑动窗口从500ms到700ms，总共2000万股，应该触发下单！

# 5. 测试场景4：窗口重置机制
# 跳跃到1000ms，应该触发窗口重置
[93001000]|SZ.000001|BW|V:25000000|T:93001000|P:110000|S:1|OT:A|N:3003
# 这笔2500万股应该重置窗口，但单独不足以触发下单（剩余封单还有0万）

# 6. 测试场景5：剩余封单不足的情况
# 先消耗大量封单，使剩余不足200万
[93001100]|SH.600002|BT|V:24000000|T:93001100|P:110000|TF:B|BN:1006|AN:2006
# 剩余封单 = 25000000 - 24000000 = 100万 < 200万，满足条件

# 再加少量成交，触发下单
[93001200]|SH.600002|BT|V:1000000|T:93001200|P:110000|TF:B|BN:1007|AN:2007
# 300ms窗口内总共2500万股，剩余封单-100万，应该触发下单！

# 7. 测试场景6：精确的300ms边界测试
# 测试301ms时的数据是否被正确排除
[93001500]|SZ.000001|BW|V:10000000|T:93001500|P:110000|S:1|OT:A|N:3004
[93001801]|SZ.000001|BW|V:15000000|T:93001801|P:110000|S:1|OT:A|N:3005
# 从1500ms到1801ms是301ms，第一笔应该被排除，只有1500万股，不应该触发
