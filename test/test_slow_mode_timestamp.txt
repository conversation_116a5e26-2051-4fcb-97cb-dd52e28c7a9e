# 慢排版模式时间戳测试
# 测试slow_mode改为时间戳模式的功能：
# 1. 在SecurityTick中，如果时刻超过5s就将slow_mode置为0
# 2. 如果阈值大于0，上板时没有达到阈值，就主动设置slow_mode

# 1. 初始tick数据 - 确保股票就绪
[93000000]|SH.600002|TICK|V:1000000|T:93000000|M:100000|TAV:1000000|BP1:110000|BV1:5000000|PC:100000
[93000000]|SZ.000001|TICK|V:800000|T:93000000|M:100000|TAV:1000000|BP1:110000|BV1:5000000|PC:100000

# 1.1 更多tick数据确保股票就绪
[93000100]|SH.600002|TICK|V:1000000|T:93000100|M:105000|TAV:1000000|BP1:110000|BV1:5000000|PC:100000
[93000100]|SZ.000001|TICK|V:800000|T:93000100|M:105000|TAV:1000000|BP1:110000|BV1:5000000|PC:100000

# 2. 设置阈值大于0的情况，测试上板时没达到阈值的slow_mode设置
# 阈值为2000万（动态调整后），封单量只有100万*11万/万=110万元 < 2000万，应该触发slow_mode
[93100000]|SH.600002|TICK|V:1000000|T:93100000|M:110000|TAV:0|BP1:110000|BV1:1000000|PC:100000

# 3. 测试slow_mode超时自动关闭（5秒后）
# 在93100000 + 6000ms = 93106000时刻，slow_mode应该被自动关闭
[93106000]|SH.600002|TICK|V:1000000|T:93106000|M:110000|TAV:0|BP1:110000|BV1:1000000|PC:100000

# 4. 再次测试上板时没达到阈值的slow_mode设置
[93200000]|SH.600002|TICK|V:1000000|T:93200000|M:110000|TAV:0|BP1:110000|BV1:800000|PC:100000

# 5. 测试slow_mode在5秒内的情况（不应该被关闭）
# 在93200000 + 3000ms = 93203000时刻，slow_mode应该仍然开启
[93203000]|SH.600002|TICK|V:1000000|T:93203000|M:110000|TAV:0|BP1:110000|BV1:800000|PC:100000

# 6. SZ市场类似测试
# 封单量120万*11万/万=132万元 < 2000万，应该触发slow_mode
[93100000]|SZ.000001|TICK|V:800000|T:93100000|M:110000|TAV:0|BP1:110000|BV1:1200000|PC:100000
[93106000]|SZ.000001|TICK|V:800000|T:93106000|M:110000|TAV:0|BP1:110000|BV1:1200000|PC:100000

# 期望结果：
# 1. 在93100000时刻，SH.600002和SZ.000001应该开启slow_mode（上板未达阈值）
# 2. 在93106000时刻，slow_mode应该被自动关闭（超过5秒）
# 3. 在93200000时刻，SH.600002应该再次开启slow_mode
# 4. 在93203000时刻，slow_mode应该仍然开启（未超过5秒）
