#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <sys/time.h>
#include <time.h>
#include <iomanip>

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

#include "../simple_strategy.cc"
// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {

    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {

        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {

        return 0;
    }
}

using namespace std;

// 简单的计时函数，传入函数指针或lambda，返回执行时间（微秒）
template<typename Func>
long long measureTime(Func func) {
    struct timeval tv;
    long long start, end;

    gettimeofday(&tv, NULL);
    start = tv.tv_usec;

    func();  // 执行传入的函数

    gettimeofday(&tv, NULL);
    end = tv.tv_usec;

    return end - start;
}

// 测量CPU周期数的函数，传入函数指针或lambda，返回执行的CPU周期数
template<typename Func>
long long measureCpuCycles(Func func) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();  // Windows使用__rdtsc()
#else
    start_cycles = __rdtsc();  // Linux也使用__rdtsc()
#endif

    func();  // 执行传入的函数

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    return end_cycles - start_cycles;
}

// 方法1: 使用 gettimeofday + localtime_s 获取东八区时间
string getTimeMethod1() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    time_t sec = tv.tv_sec;
    struct tm tm_info;
#ifdef _WIN32
    localtime_s(&tm_info, &sec);
#else
    localtime_r(&sec, &tm_info);
#endif

    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, tv.tv_usec);
    return string(buffer);
}

// 方法2: 使用 chrono + localtime_s
string getTimeMethod2() {
    auto now = chrono::system_clock::now();
    auto time_t_now = chrono::system_clock::to_time_t(now);
    auto microseconds = chrono::duration_cast<chrono::microseconds>(
        now.time_since_epoch()) % 1000000;

    struct tm tm_info;
#ifdef _WIN32
    localtime_s(&tm_info, &time_t_now);
#else
    localtime_r(&time_t_now, &tm_info);
#endif

    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, microseconds.count());
    return string(buffer);
}

// 方法3: 使用 clock_gettime + localtime_s (Windows不支持clock_gettime，用chrono替代)
string getTimeMethod3() {
#ifdef _WIN32
    // Windows使用chrono替代clock_gettime
    auto now = chrono::high_resolution_clock::now();
    auto time_t_now = chrono::system_clock::to_time_t(chrono::system_clock::now());
    auto microseconds = chrono::duration_cast<chrono::microseconds>(
        now.time_since_epoch()) % 1000000;

    struct tm tm_info;
    localtime_s(&tm_info, &time_t_now);

    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, microseconds.count());
#else
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);

    struct tm tm_info;
    localtime_r(&ts.tv_sec, &tm_info);

    long microseconds = ts.tv_nsec / 1000;
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, microseconds);
#endif
    return string(buffer);
}

// 方法4: 使用 gettimeofday + gmtime_s + 手动加8小时
string getTimeMethod4() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    time_t sec = tv.tv_sec + 8 * 3600; // 加8小时转换为东八区
    struct tm tm_info;
#ifdef _WIN32
    gmtime_s(&tm_info, &sec);
#else
    gmtime_r(&sec, &tm_info);
#endif

    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, tv.tv_usec);
    return string(buffer);
}

// 方法5: 直接使用 gettimeofday，不转换时区（UTC时间）
string getTimeMethod5() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    time_t sec = tv.tv_sec;
    struct tm tm_info;
#ifdef _WIN32
    gmtime_s(&tm_info, &sec);
#else
    gmtime_r(&sec, &tm_info);
#endif

    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%02d:%02d:%02d.%06ld",
             tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec, tv.tv_usec);
    return string(buffer);
}

int main() {
    cout << "测试获取东八区本地时分秒微秒的最快方法" << endl;
    cout << "========================================" << endl;

    const int test_rounds = 10; // 测试10次取平均值

    // 测试方法1: gettimeofday + localtime_r
    vector<long long> times1;
    string result1;
    for(int i = 0; i < test_rounds; i++) {
        long long duration = measureTime([&]() {
            result1 = getTimeMethod1();
        });
        times1.push_back(duration);
    }

    // 测试方法2: chrono + localtime_r
    vector<long long> times2;
    string result2;
    for(int i = 0; i < test_rounds; i++) {
        long long duration = measureTime([&]() {
            result2 = getTimeMethod2();
        });
        times2.push_back(duration);
    }

    // 测试方法3: clock_gettime + localtime_r
    vector<long long> times3;
    string result3;
    for(int i = 0; i < test_rounds; i++) {
        long long duration = measureTime([&]() {
            result3 = getTimeMethod3();
        });
        times3.push_back(duration);
    }

    // 测试方法4: gettimeofday + gmtime_r + 手动加8小时
    vector<long long> times4;
    string result4;
    for(int i = 0; i < test_rounds; i++) {
        long long duration = measureTime([&]() {
            result4 = getTimeMethod4();
        });
        times4.push_back(duration);
    }

    // 测试方法5: gettimeofday + gmtime_r (UTC)
    vector<long long> times5;
    string result5;
    for(int i = 0; i < test_rounds; i++) {
        long long duration = measureTime([&]() {
            result5 = getTimeMethod5();
        });
        times5.push_back(duration);
    }

    // 计算平均值的函数
    auto calculateAverage = [](const vector<long long>& times) -> double {
        long long sum = 0;
        for(auto t : times) sum += t;
        return static_cast<double>(sum) / times.size();
    };

    // 输出结果
    cout << fixed << setprecision(2);
    cout << "方法1 (gettimeofday + localtime_r): " << calculateAverage(times1) << " 微秒, 结果: " << result1 << endl;
    cout << "方法2 (chrono + localtime_r): " << calculateAverage(times2) << " 微秒, 结果: " << result2 << endl;
    cout << "方法3 (clock_gettime + localtime_r): " << calculateAverage(times3) << " 微秒, 结果: " << result3 << endl;
    cout << "方法4 (gettimeofday + gmtime_r + 8h): " << calculateAverage(times4) << " 微秒, 结果: " << result4 << endl;
    cout << "方法5 (gettimeofday + gmtime_r UTC): " << calculateAverage(times5) << " 微秒, 结果: " << result5 << endl;

    // 找出最快的方法
    vector<pair<string, double>> results = {
        {"方法1 (gettimeofday + localtime_r)", calculateAverage(times1)},
        {"方法2 (chrono + localtime_r)", calculateAverage(times2)},
        {"方法3 (clock_gettime + localtime_r)", calculateAverage(times3)},
        {"方法4 (gettimeofday + gmtime_r + 8h)", calculateAverage(times4)},
        {"方法5 (gettimeofday + gmtime_r UTC)", calculateAverage(times5)}
    };

    auto fastest = min_element(results.begin(), results.end(),
        [](const auto& a, const auto& b) { return a.second < b.second; });

    cout << endl << "最快的方法是: " << fastest->first << " (平均 " << fastest->second << " 微秒)" << endl;

    // 测试CPU周期数
    cout << endl << "========================================" << endl;
    cout << "测试CPU周期数 (每个方法测试10次取平均值)" << endl;
    cout << "========================================" << endl;

    // 测试方法1的CPU周期数
    vector<long long> cycles1;
    for(int i = 0; i < test_rounds; i++) {
        long long cycles = measureCpuCycles([&]() {
            result1 = getTimeMethod1();
        });
        cycles1.push_back(cycles);
    }

    // 测试方法2的CPU周期数
    vector<long long> cycles2;
    for(int i = 0; i < test_rounds; i++) {
        long long cycles = measureCpuCycles([&]() {
            result2 = getTimeMethod2();
        });
        cycles2.push_back(cycles);
    }

    // 测试方法3的CPU周期数
    vector<long long> cycles3;
    for(int i = 0; i < test_rounds; i++) {
        long long cycles = measureCpuCycles([&]() {
            result3 = getTimeMethod3();
        });
        cycles3.push_back(cycles);
    }

    // 测试方法4的CPU周期数
    vector<long long> cycles4;
    for(int i = 0; i < test_rounds; i++) {
        long long cycles = measureCpuCycles([&]() {
            result4 = getTimeMethod4();
        });
        cycles4.push_back(cycles);
    }

    // 测试方法5的CPU周期数
    vector<long long> cycles5;
    for(int i = 0; i < test_rounds; i++) {
        long long cycles = measureCpuCycles([&]() {
            result5 = getTimeMethod5();
        });
        cycles5.push_back(cycles);
    }

    // 计算CPU周期平均值
    auto calculateCycleAverage = [](const vector<long long>& cycles) -> double {
        long long sum = 0;
        for(auto c : cycles) sum += c;
        return static_cast<double>(sum) / cycles.size();
    };

    // 输出CPU周期结果
    cout << fixed << setprecision(0);
    cout << "方法1 (gettimeofday + localtime_s): " << calculateCycleAverage(cycles1) << " CPU周期" << endl;
    cout << "方法2 (chrono + localtime_s): " << calculateCycleAverage(cycles2) << " CPU周期" << endl;
    cout << "方法3 (clock_gettime替代 + localtime_s): " << calculateCycleAverage(cycles3) << " CPU周期" << endl;
    cout << "方法4 (gettimeofday + gmtime_s + 8h): " << calculateCycleAverage(cycles4) << " CPU周期" << endl;
    cout << "方法5 (gettimeofday + gmtime_s UTC): " << calculateCycleAverage(cycles5) << " CPU周期" << endl;

    // 找出CPU周期最少的方法
    vector<pair<string, double>> cycle_results = {
        {"方法1 (gettimeofday + localtime_s)", calculateCycleAverage(cycles1)},
        {"方法2 (chrono + localtime_s)", calculateCycleAverage(cycles2)},
        {"方法3 (clock_gettime替代 + localtime_s)", calculateCycleAverage(cycles3)},
        {"方法4 (gettimeofday + gmtime_s + 8h)", calculateCycleAverage(cycles4)},
        {"方法5 (gettimeofday + gmtime_s UTC)", calculateCycleAverage(cycles5)}
    };

    auto fastest_cycles = min_element(cycle_results.begin(), cycle_results.end(),
        [](const auto& a, const auto& b) { return a.second < b.second; });

    cout << endl << "CPU周期最少的方法是: " << fastest_cycles->first << " (平均 " << fastest_cycles->second << " CPU周期)" << endl;

    return 0;
}