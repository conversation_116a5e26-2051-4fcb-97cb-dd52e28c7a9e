# F4 高频交易策略系统 - 开发日志

## 2025-08-02 - 300ms滑动窗口算法实现与性能优化 🎉

### 核心功能
#### 300ms滑动窗口提前下单算法
- **功能描述**：实现基于300ms滑动窗口的提前下单逻辑，解决固定窗口遗漏交易机会的问题
- **触发条件**：300ms窗口内累积成交量≥2000万股 且 剩余封单<200万股
- **市场差异**：SH市场基于逐笔成交数据（type='1'），SZ市场基于逐笔委托数据（type='0'）
- **阈值控制**：使用阈值-1启用300ms窗口模式，与普通封单量检查逻辑隔离

#### 技术实现
```cpp
struct SlidingWindow {
    static const int WINDOW_SLOTS = 40;  // 40个时间槽，10ms精度
    int64_t volume_slots[WINDOW_SLOTS];  // 静态分配，无动态内存
    int64_t total_volume_300ms;          // 当前窗口总量
    int64_t last_update_time;            // 增量计算优化
};
```

### 性能优化 📈
#### O(1)增量计算算法
- **优化前**：每次O(40)全量重新计算，高频场景下延迟过高
- **优化后**：O(1)增量计算，只在cutoff_time向前移动时移除过期槽位
- **核心技术**：通过`last_update_time`跟踪窗口变化，平时直接使用加法操作
- **性能提升**：在高频交易场景下大幅降低延迟，适合每10ms一笔的极高频数据

#### 算法优势对比
```
固定窗口问题：
时间轴: [0-300ms] [300-600ms]
数据:   [1500万]  [1500万]
结果:   都不满足2000万条件 ❌

滑动窗口解决：
时间轴: [0-300ms] [300-600ms]
数据:   [1500万]  [1500万]
滑动:     [----300ms滑动窗口----]
结果:     3000万 > 2000万 ✅
```

### 测试验证 🧪
- **算法测试**：`test_sliding_window_300ms.txt` - 验证跨边界累积能力
- **性能测试**：`test_sliding_window_performance.txt` - 验证高频数据处理
- **边界测试**：`test_no_order_scenarios.txt` - 验证不应该下单的场景
- **测试结果**：6个测试场景全部通过，包括沪深市场差异化处理

### 技术修复 🛠️
- **边界条件**：修复300ms边界数据包含问题（使用>=而不是>条件）
- **订阅查找**：修复字符串比较和订阅索引查找问题
- **测试单过滤**：完善测试单价格过滤逻辑（90200/80200/90300）
- **板块互斥**：在OnTickbytickData中添加板块分组互斥检查

---

## 2025-08-01 - 涨跌停幅度分组功能完成 🚀

### 功能修正
#### 涨幅阈值修正
- **20cm股票阈值**：从6%修正为15%涨幅才触发位图标记
- **北交所排除**：明确不监听北交所股票（30cm涨跌停）
- **阈值逻辑**：10cm股票6%涨幅，20cm股票15%涨幅，北交所不监听

### 新增功能
#### 涨跌停幅度自动分组
- **功能描述**：根据股票涨跌停幅度自动分为10cm和20cm两个独立分组
- **股票识别**：10cm（主板）、20cm（创业板300、科创板688、北交所8/4开头）
- **分组逻辑**：一个配置自动分为两组，如"新能源汽车" → "新能源汽车_10CM" + "新能源汽车_20CM"
- **下单规则**：10cm和20cm分组可以同时下单，各自分组内互斥

#### 技术实现
```cpp
enum LimitType { LIMIT_10CM = 0, LIMIT_20CM = 1 };
struct SectorGroup { LimitType limit_type; /* 其他字段 */ };
```
- 修改 `AddSectorGroup` 函数，自动创建两个分组
- 修改 `do_block_monitor` 函数，只订阅匹配类型的股票
- 新增股票类型判断函数

### 测试验证
- **分组测试**：`test_limit_group_config.json`、`test_limit_group_monitor.txt`
- **阈值测试**：`test_gain_threshold_config.json`、`test_gain_threshold.txt`
- **测试结果**：
  - 10cm股票6%阈值测试通过（SH.600104, SZ.002594）
  - 20cm股票15%阈值测试通过（SZ.300750, SH.688981, SZ.300661）
  - 北交所股票正确排除（BJ.830001）
  - 边界值测试通过（6.0%和15.0%恰好触发）

---

## 2025-07-31 - 板块监控系统完成 🎉

### 新增功能
#### 板块监控系统
- **功能描述**：实现板块内股票的智能发现和互斥下单
- **涨幅位图**：使用位图存储涨幅超过6%的股票，支持100万只股票
- **自动订阅**：定时任务 `do_block_monitor` 自动发现并订阅板块内强势股票
- **互斥下单**：确保每个板块只下单一只股票

#### 技术实现
```cpp
struct SectorGroup {
    char group_name[32];
    char codes[MAX_CODES_PER_GROUP][12];
    bool has_ordered;  // 板块内互斥标记
};
static uint64_t g_gain_stock_bitmap[BITMAP_WORDS];  // 涨幅位图
```
- 在 `OnSecurityTick` 中设置涨幅位图标记
- `do_block_monitor` 扫描位图并自动订阅符合条件的股票
- 通过 `sector_group_index` 关联实现板块内互斥

### 测试验证
- **测试文件**：`test_sector_config.json`、`test_sector_monitor.txt`
- **测试结果**：SH.600104成功下单，SH.600066被板块互斥逻辑排除

### 技术修复
- 修复ARM64编译问题，适配Apple Silicon
- 添加测试模式宏避免main函数冲突

---

## 开发规范

### 提交格式
```
日期 - 功能描述 + 状态标识

### 新增功能
- 功能点1
- 功能点2

### 技术优化  
- 优化点1
- 优化点2

### 问题修复
- 修复点1
- 修复点2
```

### 状态标识
- 🎉 重大功能完成
- 🚀 新功能开发
- 🛠️ 技术优化
- 🐛 问题修复
- 📝 文档更新
- 🧪 测试完善
- 📈 性能提升
- 🔮 计划功能
