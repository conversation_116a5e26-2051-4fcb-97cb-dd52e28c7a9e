#ifndef FAST_STRING_BUILDER_H
#define FAST_STRING_BUILDER_H

#include <cstddef>

// 快速整数转字符串函数（支持负数）
int fastLongToString(char* buf, long long value);

// 通用的高效字符串构造器
class FastStringBuilder {
private:
    char* buffer;
    char* current;
    size_t capacity;
    
public:
    FastStringBuilder(char* buf, size_t cap);
    
    // 重置构造器
    void reset();
    
    // 添加字符串字面量
    FastStringBuilder& addLiteral(const char* str);
    
    // 添加字符串变量
    FastStringBuilder& addString(const char* str);
    
    // 添加长整数
    FastStringBuilder& addLong(long long value);
    
    // 添加整数
    FastStringBuilder& addInt(int value);
    
    // 添加单个字符
    FastStringBuilder& addChar(char c);
    
    // 完成构造
    int finish();
    
    // 获取当前长度
    int length() const;
};

// 宏定义简化使用
#define FAST_LOG_START(buf) FastStringBuilder builder(buf, sizeof(buf)); builder
#define FAST_LOG_FINISH() builder.finish()

// 示例：通用日志构造函数
int buildLogString(char* buffer, size_t bufSize, const char* prefix, const char* symbol, 
                   const char* field1_name, long long field1_value,
                   const char* field2_name, long long field2_value,
                   const char* field3_name, long long field3_value,
                   const char* field4_name, long long field4_value);

// 更灵活的模板版本
template<typename... Args>
int buildCustomLogString(char* buffer, size_t bufSize, Args... args) {
    FastStringBuilder builder(buffer, bufSize);
    (builder.addString(args), ...);  // C++17 fold expression
    return builder.finish();
}

#endif // FAST_STRING_BUILDER_H
