#include "fast_string_builder.h"

// 快速整数转字符串函数（支持负数）
int fastLongToString(char* buf, long long value) {
    if (value == 0) {
        buf[0] = '0';
        return 1;
    }
    
    char temp[32];
    int len = 0;
    bool negative = value < 0;
    
    if (negative) {
        value = -value;
    }
    
    // 转换数字
    while (value > 0) {
        temp[len++] = '0' + (value % 10);
        value /= 10;
    }
    
    int pos = 0;
    if (negative) {
        buf[pos++] = '-';
    }
    
    // 反转数字
    for (int i = len - 1; i >= 0; i--) {
        buf[pos++] = temp[i];
    }
    
    return pos;
}

// FastStringBuilder 实现
FastStringBuilder::FastStringBuilder(char* buf, size_t cap) 
    : buffer(buf), current(buf), capacity(cap) {
}

void FastStringBuilder::reset() {
    current = buffer;
}

FastStringBuilder& FastStringBuilder::addLiteral(const char* str) {
    while (*str && (current - buffer) < capacity - 1) {
        *current++ = *str++;
    }
    return *this;
}

FastStringBuilder& FastStringBuilder::addString(const char* str) {
    if (str) {
        while (*str && (current - buffer) < capacity - 1) {
            *current++ = *str++;
        }
    }
    return *this;
}

FastStringBuilder& FastStringBuilder::addLong(long long value) {
    current += fastLongToString(current, value);
    return *this;
}

FastStringBuilder& FastStringBuilder::addInt(int value) {
    current += fastLongToString(current, (long long)value);
    return *this;
}

FastStringBuilder& FastStringBuilder::addChar(char c) {
    if ((current - buffer) < capacity - 1) {
        *current++ = c;
    }
    return *this;
}

int FastStringBuilder::finish() {
    *current = '\0';
    return current - buffer;
}

int FastStringBuilder::length() const {
    return current - buffer;
}

// 示例：通用日志构造函数
int buildLogString(char* buffer, size_t bufSize, const char* prefix, const char* symbol, 
                   const char* field1_name, long long field1_value,
                   const char* field2_name, long long field2_value,
                   const char* field3_name, long long field3_value,
                   const char* field4_name, long long field4_value) {
    FastStringBuilder builder(buffer, bufSize);
    
    return builder.addLiteral(prefix)
                  .addString(symbol)
                  .addLiteral(", ")
                  .addLiteral(field1_name)
                  .addLong(field1_value)
                  .addLiteral(", ")
                  .addLiteral(field2_name)
                  .addLong(field2_value)
                  .addLiteral(", ")
                  .addLiteral(field3_name)
                  .addLong(field3_value)
                  .addLiteral(", ")
                  .addLiteral(field4_name)
                  .addLong(field4_value)
                  .finish();
}
