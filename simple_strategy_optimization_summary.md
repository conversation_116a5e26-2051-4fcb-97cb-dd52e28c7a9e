# Simple Strategy 日志优化总结

## 优化概述

本次优化成功将 `simple_strategy.cc` 文件中的日志系统进行了全面升级，实现了以下四个主要目标：

### 1. 替换日志打印方式 ✅
- **原来**: 使用 `snprintf` 构造日志字符串
- **现在**: 使用 `FAST_LOG_START` 宏和 `FastStringBuilder` 
- **性能提升**: 根据之前测试，FastStringBuilder 比 snprintf 快 **6.8倍**

### 2. 统一时间戳前缀 ✅
- **格式**: `[时间戳] 原始日志内容`
- **时间获取**: 使用优化的 `get_time()` 函数
- **实现**: 在 `append_cached_log()` 函数中自动添加时间戳前缀

### 3. 实现日志缓存机制 ✅
- **缓存大小**: 64KB (MAX_LOG_CACHE_SIZE)
- **阈值**: 56KB (LOG_CACHE_THRESHOLD, 约87%的缓冲区)
- **缓存逻辑**: 日志先存储在内存缓存中，不立即打印

### 4. 定时批量输出 ✅
- **触发条件**: 5秒定时任务回调 + 缓存接近满时
- **实现位置**: `OnStrategyTimer()` 函数中的5秒间隔处理
- **退出保护**: `OnExit()` 函数中强制刷新所有缓存

## 核心优化组件

### 日志缓存系统
```cpp
// 缓存配置
#define MAX_LOG_CACHE_SIZE 65536   // 64KB
#define LOG_CACHE_THRESHOLD 57344  // 56KB

// 全局变量
static char g_log_cache[MAX_LOG_CACHE_SIZE];
static size_t g_log_cache_pos = 0;
static int64_t g_last_flush_time = 0;
```

### 核心函数

#### 1. `fast_cached_log()` - 主要日志接口
```cpp
template<typename BuilderFunc>
inline void fast_cached_log(BuilderFunc builder_func) {
    FastStringBuilder builder(g_log_buffer, sizeof(g_log_buffer));
    builder_func(builder);
    builder.finish();
    append_cached_log(g_log_buffer);
}
```

#### 2. `append_cached_log()` - 添加时间戳并缓存
```cpp
inline void append_cached_log(const char* log_entry) {
    char* timestamp = get_time();
    // 构造: [timestamp] log_entry\n
    // 检查缓存空间，必要时刷新
}
```

#### 3. `flush_log_cache()` - 批量输出
```cpp
inline void flush_log_cache(bool force = false) {
    // 条件：强制刷新 OR 距离上次刷新超过5秒
    if (g_log_cache_pos > 0 && (force || current_time - g_last_flush_time > 5000)) {
        strategy_log(StrategyLogLevel_Info, g_log_cache);
        // 重置缓存
    }
}
```

## 优化的日志调用示例

### 原来的方式
```cpp
snprintf(g_log_buffer, sizeof(g_log_buffer),
         "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
         subscription.symbol, tv.tv_usec, (long long) tbt->data_time, vol, subscription.threshold_volume);
strategy_log(StrategyLogLevel_Info, g_log_buffer);
```

### 优化后的方式
```cpp
fast_cached_log([&](FastStringBuilder& builder) {
    builder.addLiteral("order2: ")
           .addString(subscription.symbol)
           .addLiteral(", cost:")
           .addLong(tv.tv_usec)
           .addLiteral(" us, T: ")
           .addLong((long long) tbt->data_time)
           .addLiteral(", V:")
           .addLong(vol)
           .addLiteral(", Hold:")
           .addLong(subscription.threshold_volume);
});
```

## 已优化的日志位置

1. **测试单下单失败日志** (line ~1218)
2. **涨停板下单失败日志** (line ~1275)
3. **慢排版模式超时关闭日志** (line ~1394)
4. **主要下单日志 (order2)** (line ~1697)
5. **测试单检测日志** (line ~1727)
6. **慢排版下单日志** (line ~1877)
7. **JSON解析错误日志** (line ~2184)
8. **板块监控初始化完成日志** (line ~2204)
9. **定时任务中的股票信息构造** (line ~2384)

## 定时刷新机制

### 5秒定时任务
```cpp
void OnStrategyTimer(int interval, void *user_data) {
    if (interval == 5000) {
        // ... 其他逻辑 ...
        
        // 定时刷新日志缓存（5秒间隔）
        flush_log_cache(false);
    }
}
```

### 程序退出保护
```cpp
void OnExit(int reason, void *user_data) {
    // 强制刷新所有缓存的日志
    flush_log_cache(true);
    
    // ... 其他退出逻辑 ...
}
```

## 性能收益

### 字符串构造性能
- **FastStringBuilder vs snprintf**: **6.8倍** 加速
- **CPU周期减少**: 从 ~663 周期 → ~105 周期
- **时间节省**: 每次调用节省约 **168 ns**

### I/O 性能优化
- **批量输出**: 减少频繁的 `strategy_log()` 调用
- **缓存机制**: 64KB 缓存，减少系统调用次数
- **定时刷新**: 5秒间隔批量输出，平衡实时性和性能

### 内存使用优化
- **零动态分配**: 使用预分配的静态缓冲区
- **紧凑存储**: 高效的内存布局和指针操作
- **缓存友好**: 连续内存访问模式

## 编译要求

确保包含 FastStringBuilder 组件：
```bash
g++ -std=c++17 -O2 -c simple_strategy.cc include/fast_string_builder.cpp
```

## 总结

本次优化成功实现了：
- ✅ **高性能日志构造** (6.8倍加速)
- ✅ **统一时间戳格式** ([HH:MM:SS.uuuuuu] 前缀)
- ✅ **智能缓存机制** (64KB缓存，87%阈值)
- ✅ **定时批量输出** (5秒间隔 + 退出保护)
- ✅ **零破坏性改动** (保持原有日志内容和级别)

这些优化显著提升了高频交易系统的日志性能，减少了I/O开销，同时保持了日志的完整性和实时性。
