/**
 * @file    common.h
 * @brief   hftsdk 公共方法和定义
 */

#ifndef HFT_SDK_COMMON_H__
#define HFT_SDK_COMMON_H__

#include <stddef.h>

/* Version macros for compile-time API version detection */
#define HFT_VERSION_MAJOR 1
#define HFT_VERSION_MINOR 0
#define HFT_VERSION_PATCH 10
#define HFT_VERSION_STR "1.0.10"

#ifdef _WIN32
#ifdef HFT_USE_STATIC_LIBRARIE
#define HFT_API extern "C"
#else  // HFT_USE_STATIC_LIBRARIE
#ifdef HFTSDK_EXPORTS
#define HFT_API extern "C" __declspec(dllexport)
#else  // HFTSDK_EXPORTS
#define HFT_API extern "C" __declspec(dllimport)
#endif  // HFTSDK_EXPORTS
#endif  // HFT_USE_STATIC_LIBRARIE
#else
#if defined(__SUNPRO_C) || defined(__SUNPRO_CC)
#define HFT_API extern "C" __global
#elif (defined(__GNUC__) && __GNUC__ >= 4) || defined(__INTEL_COMPILER)
#define HFT_API extern "C" __attribute__((visibility("default")))
#else
#define HFT_API extern "C"
#endif
#endif


// hftsdk错误消息，Windows上输出gbk编码，Linux上输出utf8编码的错误消息
HFT_API const char* hft_strerror(int err);

// hftsdk错误消息，始终输出utf8编码的错误消息
HFT_API const char* hft_strerror_utf8(int err);

#endif  // HFT_SDK_COMMON_H__
