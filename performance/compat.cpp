#include <iostream>
#include <string>
#include <vector>
#include <array>
#include <chrono>
#include <random>
#include <cstring>
#include <algorithm>

#ifdef _WIN32
#include <intrin.h>
#include <windows.h>
#else
#include <x86intrin.h>
#endif

using namespace std;

// 股票代码大小
constexpr size_t SYMBOL_SIZE = 12;

// 测试参数
constexpr size_t MAX_SYMBOLS = 64;        // 最大股票数量
constexpr size_t TEST_ITERATIONS = 1000000; // 测试迭代次数
constexpr double HIT_RATE = 0.01;         // 命中率设置为1%

// 股票代码结构
struct SymbolEntry {
    char symbol[SYMBOL_SIZE];
    bool is_active;
    
    SymbolEntry() : is_active(false) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 紧凑索引结构 - 存储股票代码和原数组索引
struct CompactIndex {
    char symbol[SYMBOL_SIZE];
    int original_index;
    
    CompactIndex() : original_index(-1) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 紧凑数组管理器 - 使用固定大小数组
class CompactArrayManager {
private:
    CompactIndex indices[MAX_SYMBOLS];  // 固定大小数组
    size_t count;                       // 当前使用的元素数量

public:
    CompactArrayManager() : count(0) {}

    // 获取当前元素数量
    size_t size() const { return count; }

    // 获取特定索引的元素
    const CompactIndex& get(size_t index) const {
        return indices[index];
    }

    // 获取所有元素的指针和大小
    const CompactIndex* data() const { return indices; }

    // 清空数组
    void clear() { count = 0; }

    // 添加元素
    void add(const CompactIndex& idx) {
        if (count < MAX_SYMBOLS) {
            indices[count++] = idx;
        }
    }

    // 移除指定位置的元素
    void remove(size_t index) {
        if (index < count) {
            // 将最后一个元素移到要删除的位置
            if (index < count - 1) {
                indices[index] = indices[count - 1];
            }
            count--;
        }
    }

    // 更新指定位置的元素
    void update(size_t index, const CompactIndex& idx) {
        if (index < count) {
            indices[index] = idx;
        }
    }

    // 模拟3秒更新一次的场景
    void periodic_update(const SymbolEntry* symbol_table, size_t table_size, double change_ratio) {
        if (count == 0) return;

        // 随机决定要更新的元素数量
        size_t changes = static_cast<size_t>(count * change_ratio);
        if (changes == 0) changes = 1;  // 至少更新一个

        // 随机选择要移除的元素
        random_device rd;
        mt19937 g(rd());
        uniform_int_distribution<size_t> dist_remove(0, count - 1);

        for (size_t i = 0; i < changes && count > 0; ++i) {
            size_t remove_idx = dist_remove(g);
            remove(remove_idx);
        }

        // 随机选择要添加的元素
        uniform_int_distribution<size_t> dist_add(0, table_size - 1);
        for (size_t i = 0; i < changes && count < MAX_SYMBOLS; ++i) {
            size_t add_idx = dist_add(g);

            // 检查是否已存在
            bool exists = false;
            for (size_t j = 0; j < count; ++j) {
                if (indices[j].original_index == static_cast<int>(add_idx)) {
                    exists = true;
                    break;
                }
            }

            if (!exists && symbol_table[add_idx].is_active) {
                CompactIndex new_idx;
                memcpy(new_idx.symbol, symbol_table[add_idx].symbol, SYMBOL_SIZE);
                new_idx.original_index = static_cast<int>(add_idx);
                add(new_idx);
            }
        }
    }
};

// 全局数据
static SymbolEntry g_symbol_table[MAX_SYMBOLS];
static size_t g_symbol_count = 0;

// 高性能股票代码比较函数
inline bool is_symbol_equal(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t* sym1_64 = reinterpret_cast<const uint64_t*>(symbol1);
    const uint64_t* sym2_64 = reinterpret_cast<const uint64_t*>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t* sym1_32 = reinterpret_cast<const uint32_t*>(symbol1 + 8);
    const uint32_t* sym2_32 = reinterpret_cast<const uint32_t*>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}

// SIMD优化的股票代码比较函数
inline bool is_symbol_equal_simd(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
#ifdef __SSE2__
    // 使用SSE2指令集优化比较
    __m128i a = _mm_loadu_si128(reinterpret_cast<const __m128i*>(symbol1));
    __m128i b = _mm_loadu_si128(reinterpret_cast<const __m128i*>(symbol2));

    // 比较整个12字节，生成掩码
    __m128i cmp = _mm_cmpeq_epi8(a, b);

    // 检查前12字节是否全部匹配
    int mask = _mm_movemask_epi8(cmp);
    return (mask & 0xFFF) == 0xFFF;
#else
    return is_symbol_equal(symbol1, symbol2);
#endif
}

// 方法1: 原始数组线性查找
int find_by_original_array(const char symbol[SYMBOL_SIZE], size_t array_size) {
    for (size_t i = 0; i < array_size; ++i) {
        if (g_symbol_table[i].is_active && is_symbol_equal(g_symbol_table[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// 方法2: 紧凑索引数组查找
int find_by_compact_array(const char symbol[SYMBOL_SIZE], const CompactArrayManager& compact_array) {
    for (size_t i = 0; i < compact_array.size(); ++i) {
        const CompactIndex& idx = compact_array.get(i);
        if (is_symbol_equal(idx.symbol, symbol)) {
            return idx.original_index;
        }
    }
    return -1;
}

// 生成随机股票代码
void generate_random_symbol(array<char, SYMBOL_SIZE>& symbol) {
    static const char charset[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.";
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dist(0, sizeof(charset) - 2);

    // 格式: XX.XXXXXX (市场.代码)
    symbol[0] = charset[dist(gen) % 26]; // 字母
    symbol[1] = charset[dist(gen) % 26]; // 字母
    symbol[2] = '.';
    for (int i = 3; i < 9; ++i) {
        symbol[i] = charset[dist(gen) % 10 + 26]; // 数字
    }
    symbol[9] = '\0';

    // 确保剩余字节为0
    for (int i = 10; i < SYMBOL_SIZE; ++i) {
        symbol[i] = '\0';
    }
}

// 初始化测试数据
void init_test_data(size_t num_symbols, size_t compact_size) {
    // 清空数据
    g_symbol_count = 0;

    // 生成随机股票代码
    for (size_t i = 0; i < num_symbols; ++i) {
        array<char, SYMBOL_SIZE> temp_symbol;
        generate_random_symbol(temp_symbol);
        memcpy(g_symbol_table[i].symbol, temp_symbol.data(), SYMBOL_SIZE);
        g_symbol_table[i].is_active = true;
    }

    g_symbol_count = num_symbols;
}

// 运行性能测试
void run_performance_test(size_t original_size, size_t compact_size) {
    cout << "======= 测试 原始数组:" << original_size << "只股票 vs 紧凑数组:" << compact_size << "只股票 =======" << endl;

    // 初始化测试数据
    init_test_data(original_size, compact_size);

    // 创建紧凑索引数组
    CompactArrayManager compact_array;

    // 从原始数组中随机选择compact_size个元素构建紧凑索引
    vector<size_t> indices(original_size);
    for (size_t i = 0; i < original_size; ++i) {
        indices[i] = i;
    }

    // 随机打乱并取前compact_size个
    random_device rd;
    mt19937 g(rd());
    shuffle(indices.begin(), indices.end(), g);

    for (size_t i = 0; i < compact_size; ++i) {
        size_t idx = indices[i];
        CompactIndex compact_idx;
        memcpy(compact_idx.symbol, g_symbol_table[idx].symbol, SYMBOL_SIZE);
        compact_idx.original_index = static_cast<int>(idx);
        compact_array.add(compact_idx);
    }

    // 准备测试样本 - 1%命中率
    size_t hit_samples = static_cast<size_t>(TEST_ITERATIONS * HIT_RATE);
    size_t miss_samples = TEST_ITERATIONS - hit_samples;

    vector<array<char, SYMBOL_SIZE>> test_samples(TEST_ITERATIONS);

    // 生成命中样本 - 从紧凑数组中选择
    for (size_t i = 0; i < hit_samples; ++i) {
        size_t idx = i % compact_size;
        memcpy(test_samples[i].data(), compact_array.get(idx % compact_array.size()).symbol, SYMBOL_SIZE);
    }

    // 生成未命中样本 - 随机生成
    for (size_t i = hit_samples; i < TEST_ITERATIONS; ++i) {
        generate_random_symbol(test_samples[i]);
    }

    // 随机打乱测试样本
    shuffle(test_samples.begin(), test_samples.end(), g);

    // 测试方法1: 原始数组线性查找
    auto start = chrono::high_resolution_clock::now();
    int hits1 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_original_array(test_samples[i].data(), original_size);
        if (result >= 0) hits1++;
    }
    auto end = chrono::high_resolution_clock::now();
    double time1 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法2: 紧凑索引数组查找
    start = chrono::high_resolution_clock::now();
    int hits2 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_compact_array(test_samples[i].data(), compact_array);
        if (result >= 0) hits2++;
    }
    end = chrono::high_resolution_clock::now();
    double time2 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 输出结果
    cout << "原始数组查找:  " << time1 << " ns/op, 命中: " << hits1 << " (" << (hits1 * 100.0 / TEST_ITERATIONS) << "%)" << endl;
    cout << "紧凑数组查找:  " << time2 << " ns/op, 命中: " << hits2 << " (" << (hits2 * 100.0 / TEST_ITERATIONS) << "%)" << endl;
    cout << "加速比:       " << (time1 / time2) << "x" << endl;
    cout << endl;

    // 验证结果一致性
    if (hits1 != hits2) {
        cout << "警告: 两种方法返回的结果不一致!" << endl;
    }
}

// 测试定期更新的影响
void run_periodic_update_test(size_t original_size, size_t compact_size) {
    cout << "======= 测试定期更新对性能的影响 (原始数组:" << original_size << ", 紧凑数组:" << compact_size << ") =======" << endl;

    // 初始化测试数据
    init_test_data(original_size, compact_size);

    // 创建紧凑索引数组
    CompactArrayManager compact_array;

    // 从原始数组中随机选择compact_size个元素构建紧凑索引
    vector<size_t> indices(original_size);
    for (size_t i = 0; i < original_size; ++i) {
        indices[i] = i;
    }

    // 随机打乱并取前compact_size个
    random_device rd;
    mt19937 g(rd());
    shuffle(indices.begin(), indices.end(), g);

    for (size_t i = 0; i < compact_size; ++i) {
        size_t idx = indices[i];
        CompactIndex compact_idx;
        memcpy(compact_idx.symbol, g_symbol_table[idx].symbol, SYMBOL_SIZE);
        compact_idx.original_index = static_cast<int>(idx);
        compact_array.add(compact_idx);
    }

    // 准备测试样本 - 1%命中率
    vector<array<char, SYMBOL_SIZE>> test_samples(TEST_ITERATIONS);

    // 测试不同的更新比例
    vector<double> update_ratios = {0.1, 0.2, 0.3, 0.5};

    for (double update_ratio : update_ratios) {
        cout << "更新比例: " << (update_ratio * 100) << "%" << endl;

        // 重置紧凑数组
        compact_array.clear();
        shuffle(indices.begin(), indices.end(), g);

        for (size_t i = 0; i < compact_size; ++i) {
            size_t idx = indices[i];
            CompactIndex compact_idx;
            memcpy(compact_idx.symbol, g_symbol_table[idx].symbol, SYMBOL_SIZE);
            compact_idx.original_index = static_cast<int>(idx);
            compact_array.add(compact_idx);
        }

        // 生成命中样本 - 从紧凑数组中选择
        size_t hit_samples = static_cast<size_t>(TEST_ITERATIONS * HIT_RATE);
        for (size_t i = 0; i < hit_samples; ++i) {
            size_t idx = i % compact_array.size();
            memcpy(test_samples[i].data(), compact_array.get(idx).symbol, SYMBOL_SIZE);
        }

        // 生成未命中样本 - 随机生成
        for (size_t i = hit_samples; i < TEST_ITERATIONS; ++i) {
            generate_random_symbol(test_samples[i]);
        }

        // 随机打乱测试样本
        shuffle(test_samples.begin(), test_samples.end(), g);

        // 测试方法1: 原始数组线性查找
        auto start = chrono::high_resolution_clock::now();
        int hits1 = 0;
        for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
            // 模拟每3秒更新一次
            if (i % (TEST_ITERATIONS / 10) == 0) {
                // 什么都不做，原始数组不需要更新
            }

            int result = find_by_original_array(test_samples[i].data(), original_size);
            if (result >= 0) hits1++;
        }
        auto end = chrono::high_resolution_clock::now();
        double time1 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

        // 测试方法2: 紧凑索引数组查找
        start = chrono::high_resolution_clock::now();
        int hits2 = 0;
        for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
            // 模拟每3秒更新一次
            if (i % (TEST_ITERATIONS / 10) == 0) {
                compact_array.periodic_update(g_symbol_table, original_size, update_ratio);
            }

            int result = find_by_compact_array(test_samples[i].data(), compact_array);
            if (result >= 0) hits2++;
        }
        end = chrono::high_resolution_clock::now();
        double time2 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

        // 输出结果
        cout << "  原始数组查找:  " << time1 << " ns/op, 命中: " << hits1 << " (" << (hits1 * 100.0 / TEST_ITERATIONS) << "%)" << endl;
        cout << "  紧凑数组查找:  " << time2 << " ns/op, 命中: " << hits2 << " (" << (hits2 * 100.0 / TEST_ITERATIONS) << "%)" << endl;
        cout << "  加速比:       " << (time1 / time2) << "x" << endl;
        cout << endl;
    }
}

// 测试不同大小的紧凑数组
void run_compact_ratio_tests(size_t original_size) {
    cout << "======= 测试不同紧凑比例 (原始数组大小: " << original_size << ") =======" << endl;

    vector<double> compact_ratios = {0.05, 0.1, 0.2, 0.3, 0.5, 0.8};

    for (double ratio : compact_ratios) {
        size_t compact_size = static_cast<size_t>(original_size * ratio);
        if (compact_size < 1) compact_size = 1;

        run_performance_test(original_size, compact_size);
    }
}

int main() {
    // 设置控制台编码为UTF-8，解决中文乱码问题
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
#endif

    cout << "紧凑数组性能测试: 比较原始数组查找和紧凑索引数组查找" << endl;
    cout << "测试迭代次数: " << TEST_ITERATIONS << endl;
    cout << "命中率: " << (HIT_RATE * 100) << "%" << endl << endl;

    // 测试8个元素的原始数组和不同大小的紧凑数组
    run_compact_ratio_tests(8);

    // 测试16个元素的原始数组和不同大小的紧凑数组
    run_compact_ratio_tests(16);

    // 测试32个元素的原始数组和不同大小的紧凑数组
    run_compact_ratio_tests(32);

    // 测试64个元素的原始数组和不同大小的紧凑数组
    run_compact_ratio_tests(64);

    // 测试定期更新的影响
    run_periodic_update_test(64, 8);  // 64个原始元素，8个紧凑元素
    
    cout << "按任意键退出..." << endl;
    getchar();
    
    return 0;
} 