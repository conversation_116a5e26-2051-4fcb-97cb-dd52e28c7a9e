#include <iostream>
#include <cstring>
#include <cassert>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <random>
#include <vector>

using namespace std;

// 测试位图实现
constexpr size_t MAX_STOCK_CODES = 1000000;
constexpr size_t BITMAP_WORDS = (MAX_STOCK_CODES + 63) / 64;

static uint64_t g_gain_stock_bitmap[BITMAP_WORDS];

// 将股票代码转换为位图索引
inline int symbol_to_int(const char symbol[12]) {
    const char* code = symbol + 3;
    
    int index = 0;
    for (int i = 0; i < 6; ++i) {
        if (code[i] >= '0' && code[i] <= '9') {
            index = index * 10 + (code[i] - '0');
        } else {
            return -1;
        }
    }
    
    return index;
}

// 设置位图中的某一位
inline void set_gain_stock_bit(int index) {
    if (index < 0 || index >= MAX_STOCK_CODES) return;
    int word_index = index / 64;
    int bit_index = index % 64;
    g_gain_stock_bitmap[word_index] |= (1ULL << bit_index);
}

// 检查位图中的某一位
inline bool is_gain_stock_set(int index) {
    if (index < 0 || index >= MAX_STOCK_CODES) return false;
    int word_index = index / 64;
    int bit_index = index % 64;
    return (g_gain_stock_bitmap[word_index] & (1ULL << bit_index)) != 0;
}

// 清空整个位图
inline void clear_all_gain_stocks() {
    memset(g_gain_stock_bitmap, 0, sizeof(g_gain_stock_bitmap));
}

int main() {
    // 测试用例
    const char* test_symbols[] = {
        "SH.600000",  // 浦发银行 -> 600000
        "SZ.000001",  // 平安银行 -> 1
        "SH.600519",  // 贵州茅台 -> 600519
        "SZ.000002",  // 万科A -> 2
        "SH.600036"   // 招商银行 -> 600036
    };
    
    int expected_indices[] = {600000, 1, 600519, 2, 600036};
    
    cout << "=== 位图测试开始 ===" << endl;
    
    // 测试索引转换
    for (int i = 0; i < 5; ++i) {
        int index = symbol_to_int(test_symbols[i]);
        cout << "股票: " << test_symbols[i] << " -> 索引: " << index << endl;
        assert(index == expected_indices[i]);
    }
    
    // 测试位图设置和检查
    clear_all_gain_stocks();
    
    for (int i = 0; i < 5; ++i) {
        int index = expected_indices[i];
        
        // 初始应该是未设置的
        assert(!is_gain_stock_set(index));
        
        // 设置位图
        set_gain_stock_bit(index);
        
        // 现在应该是设置的
        assert(is_gain_stock_set(index));
        
        cout << "位图测试通过: 索引 " << index << endl;
    }
    
    // 测试边界情况
    assert(symbol_to_int("SH.000000") == 0);
    assert(symbol_to_int("SZ.999999") == 999999);
    assert(symbol_to_int("SH.ABCDEF") == -1); // 无效代码
    
    // 测试清空
    clear_all_gain_stocks();
    for (int i = 0; i < 5; ++i) {
        assert(!is_gain_stock_set(expected_indices[i]));
    }
    
    cout << "=== 所有测试通过！ ===" << endl;
    cout << "位图大小: " << sizeof(g_gain_stock_bitmap) / 1024 << " KB" << endl;
    cout << "支持股票数量: " << MAX_STOCK_CODES << endl;

    // 性能比较测试
    cout << "\n=== 性能比较测试 ===" << endl;

    // 生成测试数据
    vector<string> perf_test_symbols;
    vector<int> perf_test_indices;

    // 生成5000个随机股票代码（模拟实际市场规模）
    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> dis(0, 999999);

    for (int i = 0; i < 5000; ++i) {
        int code = dis(gen);
        char symbol[20];
        if (code < 500000) {
            snprintf(symbol, sizeof(symbol), "SH.%06d", code);
        } else {
            snprintf(symbol, sizeof(symbol), "SZ.%06d", code);
        }
        perf_test_symbols.push_back(string(symbol));
        perf_test_indices.push_back(code);
    }

    cout << "生成测试数据: " << perf_test_symbols.size() << " 个股票代码" << endl;

    // 测试1: 位图插入性能
    clear_all_gain_stocks();
    auto start_time = chrono::high_resolution_clock::now();

    for (int i = 0; i < perf_test_symbols.size(); ++i) {
        int index = symbol_to_int(perf_test_symbols[i].c_str());
        if (index >= 0) {
            set_gain_stock_bit(index);
        }
    }

    auto end_time = chrono::high_resolution_clock::now();
    auto bitmap_insert_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    cout << "位图插入时间: " << bitmap_insert_time.count() << " 微秒" << endl;

    // 测试2: unordered_set插入性能 (使用string作为key)
    unordered_set<string> hash_set;
    start_time = chrono::high_resolution_clock::now();

    for (int i = 0; i < perf_test_symbols.size(); ++i) {
        hash_set.insert(perf_test_symbols[i]);
    }

    end_time = chrono::high_resolution_clock::now();
    auto hashset_insert_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    cout << "unordered_set插入时间: " << hashset_insert_time.count() << " 微秒" << endl;

    // 测试3: 位图查询性能
    start_time = chrono::high_resolution_clock::now();

    int bitmap_found = 0;
    for (int i = 0; i < perf_test_symbols.size(); ++i) {
        int index = symbol_to_int(perf_test_symbols[i].c_str());
        if (index >= 0 && is_gain_stock_set(index)) {
            bitmap_found++;
        }
    }

    end_time = chrono::high_resolution_clock::now();
    auto bitmap_query_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    cout << "位图查询时间: " << bitmap_query_time.count() << " 微秒 (找到 " << bitmap_found << " 个)" << endl;

    // 测试4: unordered_set查询性能 (使用string作为key)
    start_time = chrono::high_resolution_clock::now();

    int hashset_found = 0;
    for (int i = 0; i < perf_test_symbols.size(); ++i) {
        if (hash_set.find(perf_test_symbols[i]) != hash_set.end()) {
            hashset_found++;
        }
    }

    end_time = chrono::high_resolution_clock::now();
    auto hashset_query_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    cout << "unordered_set查询时间: " << hashset_query_time.count() << " 微秒 (找到 " << hashset_found << " 个)" << endl;

    // 测试5: 批量清空性能
    start_time = chrono::high_resolution_clock::now();
    clear_all_gain_stocks();
    end_time = chrono::high_resolution_clock::now();
    auto bitmap_clear_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    start_time = chrono::high_resolution_clock::now();
    hash_set.clear();
    end_time = chrono::high_resolution_clock::now();
    auto hashset_clear_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);

    cout << "位图清空时间: " << bitmap_clear_time.count() << " 微秒" << endl;
    cout << "unordered_set清空时间: " << hashset_clear_time.count() << " 微秒" << endl;

    // 内存使用比较
    cout << "\n=== 内存使用比较 ===" << endl;
    cout << "位图内存使用: " << sizeof(g_gain_stock_bitmap) / 1024 << " KB (固定)" << endl;
    cout << "unordered_set内存使用: ~" << (hash_set.size() * (sizeof(string) + sizeof(void*)) + hash_set.bucket_count() * sizeof(void*)) / 1024 << " KB (动态)" << endl;

    // 性能总结
    cout << "\n=== 性能总结 ===" << endl;
    cout << "插入性能提升: " << (double)hashset_insert_time.count() / bitmap_insert_time.count() << "x" << endl;
    cout << "查询性能提升: " << (double)hashset_query_time.count() / bitmap_query_time.count() << "x" << endl;
    cout << "清空性能提升: " << (double)hashset_clear_time.count() / bitmap_clear_time.count() << "x" << endl;

    return 0;
}
