#include <iostream>
#include <chrono>
#include <vector>
#include <random>
#include <cassert>
#include <cstring>

using namespace std;

// 当前版本：使用for循环
inline int symbol_to_bitmap_index_loop(const char symbol[12]) {
    // 跳过市场前缀（SH. 或 SZ.），提取6位数字
    const char* code = symbol + 3;
    
    // 手动转换6位数字字符串为整数，避免atoi的开销
    int index = 0;
    for (int i = 0; i < 6; ++i) {
        if (code[i] >= '0' && code[i] <= '9') {
            index = index * 10 + (code[i] - '0');
        } else {
            return -1; // 无效的股票代码
        }
    }
    
    return index;
}

// 优化版本：直接下标计算
inline int symbol_to_bitmap_index_direct(const char symbol[12]) {
    // 跳过市场前缀（SH. 或 SZ.），提取6位数字
    const char* code = symbol + 3;
    
    // 直接验证所有字符都是数字
    if (code[0] < '0' || code[0] > '9' ||
        code[1] < '0' || code[1] > '9' ||
        code[2] < '0' || code[2] > '9' ||
        code[3] < '0' || code[3] > '9' ||
        code[4] < '0' || code[4] > '9' ||
        code[5] < '0' || code[5] > '9') {
        return -1; // 无效的股票代码
    }
    
    // 直接计算索引，展开循环
    return (code[0] - '0') * 100000 +
           (code[1] - '0') * 10000 +
           (code[2] - '0') * 1000 +
           (code[3] - '0') * 100 +
           (code[4] - '0') * 10 +
           (code[5] - '0');
}

// 进一步优化版本：使用位运算优化
inline int symbol_to_bitmap_index_bitwise(const char symbol[12]) {
    const char* code = symbol + 3;
    
    // 快速验证：所有字符都应该在'0'-'9'范围内
    uint64_t chars = *(uint64_t*)(code - 2); // 读取8字节，包含6位数字
    
    // 检查每个字符是否在'0'-'9'范围内
    // 这里简化处理，只检查前6个字符
    for (int i = 0; i < 6; ++i) {
        if (code[i] < '0' || code[i] > '9') {
            return -1;
        }
    }
    
    // 使用位运算优化的计算
    int result = 0;
    result += (code[0] & 0x0F) * 100000;
    result += (code[1] & 0x0F) * 10000;
    result += (code[2] & 0x0F) * 1000;
    result += (code[3] & 0x0F) * 100;
    result += (code[4] & 0x0F) * 10;
    result += (code[5] & 0x0F);
    
    return result;
}

// 生成测试数据
vector<string> generate_test_symbols(int count) {
    vector<string> symbols;
    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> dis(0, 999999);
    
    for (int i = 0; i < count; ++i) {
        int code = dis(gen);
        char symbol[20];
        if (i % 2 == 0) {
            snprintf(symbol, sizeof(symbol), "SH.%06d", code);
        } else {
            snprintf(symbol, sizeof(symbol), "SZ.%06d", code);
        }
        symbols.push_back(string(symbol));
    }
    
    return symbols;
}

// 正确性测试
void correctness_test() {
    cout << "=== 正确性测试 ===" << endl;
    
    const char* test_cases[] = {
        "SH.600000",
        "SZ.000001", 
        "SH.600519",
        "SZ.000002",
        "SH.999999",
        "SZ.123456",
        "SH.ABCDEF", // 无效案例
        "SZ.12345A"  // 无效案例
    };
    
    int expected[] = {600000, 1, 600519, 2, 999999, 123456, -1, -1};
    
    for (int i = 0; i < 8; ++i) {
        int result_loop = symbol_to_bitmap_index_loop(test_cases[i]);
        int result_direct = symbol_to_bitmap_index_direct(test_cases[i]);
        int result_bitwise = symbol_to_bitmap_index_bitwise(test_cases[i]);
        
        cout << "测试: " << test_cases[i] 
             << " -> Loop: " << result_loop
             << ", Direct: " << result_direct  
             << ", Bitwise: " << result_bitwise
             << ", Expected: " << expected[i] << endl;
             
        assert(result_loop == expected[i]);
        assert(result_direct == expected[i]);
        assert(result_bitwise == expected[i]);
    }
    
    cout << "所有正确性测试通过！" << endl;
}

// 性能测试
void performance_test() {
    cout << "\n=== 性能测试 ===" << endl;
    
    const int test_count = 1000000; // 100万次测试
    vector<string> test_symbols = generate_test_symbols(test_count);
    
    cout << "生成测试数据: " << test_symbols.size() << " 个股票代码" << endl;
    
    // 转换为char数组以避免string操作的开销
    vector<vector<char>> symbol_arrays(test_count, vector<char>(12, 0));
    for (int i = 0; i < test_count; ++i) {
        strncpy(symbol_arrays[i].data(), test_symbols[i].c_str(), 11);
    }
    
    // 测试1: for循环版本
    auto start_time = chrono::high_resolution_clock::now();
    volatile int sum1 = 0; // 防止编译器优化
    
    for (int i = 0; i < test_count; ++i) {
        int result = symbol_to_bitmap_index_loop(symbol_arrays[i].data());
        sum1 += result;
    }
    
    auto end_time = chrono::high_resolution_clock::now();
    auto loop_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);
    
    // 测试2: 直接计算版本
    start_time = chrono::high_resolution_clock::now();
    volatile int sum2 = 0;
    
    for (int i = 0; i < test_count; ++i) {
        int result = symbol_to_bitmap_index_direct(symbol_arrays[i].data());
        sum2 += result;
    }
    
    end_time = chrono::high_resolution_clock::now();
    auto direct_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);
    
    // 测试3: 位运算版本
    start_time = chrono::high_resolution_clock::now();
    volatile int sum3 = 0;
    
    for (int i = 0; i < test_count; ++i) {
        int result = symbol_to_bitmap_index_bitwise(symbol_arrays[i].data());
        sum3 += result;
    }
    
    end_time = chrono::high_resolution_clock::now();
    auto bitwise_time = chrono::duration_cast<chrono::microseconds>(end_time - start_time);
    
    // 验证结果一致性
    cout << "结果验证 - Loop sum: " << sum1 << ", Direct sum: " << sum2 << ", Bitwise sum: " << sum3 << endl;
    assert(sum1 == sum2 && sum2 == sum3);
    
    // 输出性能结果
    cout << "\n=== 性能结果 (" << test_count << " 次调用) ===" << endl;
    cout << "For循环版本:   " << loop_time.count() << " 微秒" << endl;
    cout << "直接计算版本:  " << direct_time.count() << " 微秒" << endl;
    cout << "位运算版本:    " << bitwise_time.count() << " 微秒" << endl;
    
    cout << "\n=== 性能提升 ===" << endl;
    cout << "直接计算 vs For循环: " << (double)loop_time.count() / direct_time.count() << "x" << endl;
    cout << "位运算 vs For循环:   " << (double)loop_time.count() / bitwise_time.count() << "x" << endl;
    cout << "位运算 vs 直接计算:  " << (double)direct_time.count() / bitwise_time.count() << "x" << endl;
    
    cout << "\n=== 平均每次调用时间 ===" << endl;
    cout << "For循环版本:   " << (double)loop_time.count() / test_count << " 微秒/次" << endl;
    cout << "直接计算版本:  " << (double)direct_time.count() / test_count << " 微秒/次" << endl;
    cout << "位运算版本:    " << (double)bitwise_time.count() / test_count << " 微秒/次" << endl;
}

int main() {
    correctness_test();
    performance_test();
    
    return 0;
}
