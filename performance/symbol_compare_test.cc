#include <iostream>
#include <chrono>
#include <cstring>
#include <vector>
#include <random>
#include <iomanip>
#include <algorithm>

using namespace std;
using namespace std::chrono;

// 方法1: 优化的64位比较（只比较第1-8位）
inline bool is_symbol_equal_optimized(const char symbol1[12], const char symbol2[12]) {
    // 使用64位比较，从第1位开始比较8字节
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1 + 1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2 + 1);
    
    // 比较第1-8位（8字节）
    return sym1_64[0] == sym2_64[0];
}

// 方法2: 传统的64位+32位比较（全部12字节）
inline bool is_symbol_equal_full(const char symbol1[12], const char symbol2[12]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t *sym1_32 = reinterpret_cast<const uint32_t *>(symbol1 + 8);
    const uint32_t *sym2_32 = reinterpret_cast<const uint32_t *>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}

// 方法3: 使用memcmp
inline bool is_symbol_equal_memcmp(const char symbol1[12], const char symbol2[12]) {
    return memcmp(symbol1, symbol2, 12) == 0;
}

// 方法4: 使用strncmp
inline bool is_symbol_equal_strncmp(const char symbol1[12], const char symbol2[12]) {
    return strncmp(symbol1, symbol2, 12) == 0;
}

// 方法5: 逐字节比较
inline bool is_symbol_equal_byte_by_byte(const char symbol1[12], const char symbol2[12]) {
    for (int i = 0; i < 12; i++) {
        if (symbol1[i] != symbol2[i]) return false;
    }
    return true;
}

// 方法6: 展开的逐字节比较
inline bool is_symbol_equal_unrolled(const char symbol1[12], const char symbol2[12]) {
    return symbol1[0] == symbol2[0] && symbol1[1] == symbol2[1] && 
           symbol1[2] == symbol2[2] && symbol1[3] == symbol2[3] &&
           symbol1[4] == symbol2[4] && symbol1[5] == symbol2[5] &&
           symbol1[6] == symbol2[6] && symbol1[7] == symbol2[7] &&
           symbol1[8] == symbol2[8] && symbol1[9] == symbol2[9] &&
           symbol1[10] == symbol2[10] && symbol1[11] == symbol2[11];
}

// 方法7: 只比较前9位（股票代码部分）
inline bool is_symbol_equal_9bytes(const char symbol1[12], const char symbol2[12]) {
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);
    
    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;
    
    // 比较第9字节
    return symbol1[8] == symbol2[8];
}

// 生成测试数据
vector<pair<char[12], char[12]>> generate_test_data(int count) {
    vector<pair<char[12], char[12]>> data;
    data.reserve(count);
    
    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> dis(0, 1);
    
    // 预定义的股票代码模板
    vector<string> templates = {
        "SH.600000", "SH.600001", "SH.600002", "SH.600003",
        "SZ.000001", "SZ.000002", "SZ.000003", "SZ.000004",
        "SH.688001", "SH.688002", "SZ.300001", "SZ.300002"
    };
    
    for (int i = 0; i < count; i++) {
        pair<char[12], char[12]> p;
        
        // 随机选择模板
        string base = templates[i % templates.size()];
        
        // 填充第一个字符串
        memset(p.first, 0, 12);
        memcpy(p.first, base.c_str(), min(base.length(), (size_t)11));
        
        // 50%概率生成相同的字符串，50%概率生成不同的
        if (dis(gen) == 0) {
            // 生成相同的字符串
            memcpy(p.second, p.first, 12);
        } else {
            // 生成不同的字符串（修改最后一位数字）
            memcpy(p.second, p.first, 12);
            if (p.second[8] >= '0' && p.second[8] <= '8') {
                p.second[8]++;
            } else {
                p.second[8] = '0';
            }
        }
        
        data.push_back(move(p));
    }
    
    return data;
}

// 性能测试函数模板
template<typename Func>
double benchmark_function(Func func, const vector<pair<char[12], char[12]>>& data, 
                         const string& name, int iterations = 1000) {
    auto start = high_resolution_clock::now();
    
    volatile bool result = false; // 防止编译器优化
    
    for (int iter = 0; iter < iterations; iter++) {
        for (const auto& p : data) {
            result = func(p.first, p.second);
        }
    }
    
    auto end = high_resolution_clock::now();
    auto duration = duration_cast<nanoseconds>(end - start);
    
    double avg_ns = static_cast<double>(duration.count()) / (iterations * data.size());
    
    cout << setw(25) << name << ": " 
         << setw(8) << fixed << setprecision(2) << avg_ns << " ns/op" << endl;
    
    return avg_ns;
}

int main() {
    cout << "=== 股票代码比较函数性能测试 ===" << endl;
    cout << "测试数据: 10000 对字符串" << endl;
    cout << "迭代次数: 1000 次" << endl;
    cout << "字符串格式: SH.600000 等股票代码" << endl;
    cout << endl;
    
    // 生成测试数据
    const int test_data_size = 10000;
    const int iterations = 1000;
    auto test_data = generate_test_data(test_data_size);
    
    cout << "开始性能测试..." << endl;
    cout << string(50, '-') << endl;
    
    // 测试各种方法
    vector<pair<string, double>> results;
    
    results.emplace_back("优化64位(1-8位)", 
        benchmark_function(is_symbol_equal_optimized, test_data, "优化64位(1-8位)", iterations));
    
    results.emplace_back("完整64+32位", 
        benchmark_function(is_symbol_equal_full, test_data, "完整64+32位", iterations));
    
    results.emplace_back("前9位比较", 
        benchmark_function(is_symbol_equal_9bytes, test_data, "前9位比较", iterations));
    
    results.emplace_back("memcmp", 
        benchmark_function(is_symbol_equal_memcmp, test_data, "memcmp", iterations));
    
    results.emplace_back("strncmp", 
        benchmark_function(is_symbol_equal_strncmp, test_data, "strncmp", iterations));
    
    results.emplace_back("逐字节比较", 
        benchmark_function(is_symbol_equal_byte_by_byte, test_data, "逐字节比较", iterations));
    
    results.emplace_back("展开逐字节", 
        benchmark_function(is_symbol_equal_unrolled, test_data, "展开逐字节", iterations));
    
    cout << string(50, '-') << endl;
    
    // 排序并显示结果
    sort(results.begin(), results.end(), 
         [](const pair<string, double>& a, const pair<string, double>& b) {
             return a.second < b.second;
         });
    
    cout << "\n=== 性能排名（从快到慢） ===" << endl;
    for (size_t i = 0; i < results.size(); i++) {
        cout << (i + 1) << ". " << setw(20) << results[i].first 
             << ": " << setw(8) << fixed << setprecision(2) << results[i].second << " ns/op";
        if (i > 0) {
            double speedup = results[i].second / results[0].second;
            cout << " (慢 " << setprecision(1) << speedup << "x)";
        }
        cout << endl;
    }
    
    cout << "\n=== 正确性验证 ===" << endl;
    // 验证所有方法的正确性
    char test1[12] = "SH.600000";
    char test2[12] = "SH.600000";
    char test3[12] = "SH.600001";
    
    cout << "测试相同字符串: " << test1 << " vs " << test2 << endl;
    cout << "优化64位: " << is_symbol_equal_optimized(test1, test2) << endl;
    cout << "完整比较: " << is_symbol_equal_full(test1, test2) << endl;
    cout << "memcmp: " << is_symbol_equal_memcmp(test1, test2) << endl;
    
    cout << "\n测试不同字符串: " << test1 << " vs " << test3 << endl;
    cout << "优化64位: " << is_symbol_equal_optimized(test1, test3) << endl;
    cout << "完整比较: " << is_symbol_equal_full(test1, test3) << endl;
    cout << "memcmp: " << is_symbol_equal_memcmp(test1, test3) << endl;
    
    return 0;
}
