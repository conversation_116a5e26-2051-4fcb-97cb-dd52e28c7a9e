//
// Created by <PERSON><PERSON> on 25-7-25.
//
#include <iostream>
#include <chrono>
#include <random>
#include <vector>
#include <cstring>
#include <algorithm>
#include <sys/time.h>

#ifdef _WIN32
#include <intrin.h>
#include <windows.h>
#else
#include <x86intrin.h>
#endif

using namespace std;
struct timeval tv;
long long start;


int main() {
    gettimeofday(&tv, NULL);
    start = tv.tv_usec;

    gettimeofday(&tv, NULL);
    long long end1 = tv.tv_usec;
    gettimeofday(&tv, NULL);
    long long end2 = tv.tv_usec;
    gettimeofday(&tv, NULL);
    long long end3 = tv.tv_usec;
    gettimeofday(&tv, NULL);
    long long end4 = tv.tv_usec;

    for ( int  i = 0 ; i < 100; i ++) {
        gettimeofday(&tv, NULL);
    }



    cout << end1-start << ' ' << end2 - end1 << ' ' << end3 - end2<< ' ' << end4-end3<<endl;

    cout << tv.tv_usec - end4;
}