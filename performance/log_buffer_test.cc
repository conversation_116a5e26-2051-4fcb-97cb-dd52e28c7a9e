#include <iostream>
#include <sys/time.h>
#include <string.h>
#include <stdarg.h>
#include <unistd.h>
#include <thread>
#include <chrono>

// 模拟市场数据结构
struct TickByTickData {
    char symbol[12];
    char type;
    char res[3];
    int64_t data_time;
    
    union {
        struct {
            int32_t channel_no;
            char side;
            char ord_type;
            char res[3];
            int64_t seq;
            int64_t price;
            int64_t qty;
            int64_t order_no;
            int64_t order_index;
        } entrust;
        
        struct {
            int32_t channel_no;
            char trade_flag;
            char res[3];
            int64_t seq;
            int64_t price;
            int64_t qty;
            int64_t money;
            int64_t bid_no;
            int64_t ask_no;
        } trade;
    } data;
};

// 日志缓冲区配置
#define MAX_LOG_BUFFER_SIZE 6553  // 64KB
#define LOG_BUFFER_THRESHOLD 5734  // 56KB (约87%的缓冲区)

// 预分配的日志缓冲区
static char g_l2_log_buffer[MAX_LOG_BUFFER_SIZE];
static size_t g_l2_log_buffer_pos = 0;  // 当前缓冲区位置
static int64_t g_last_flush_time = 0;   // 上次刷新时间
static int g_log_count = 0;             // 日志计数器
static int g_flush_count = 0;           // 刷新计数器

// 模拟strategy_log函数，使用cout输出
void strategy_log(int level, const char* message) {
    std::cout << "[FLUSH #" << ++g_flush_count << "] " << message << std::endl;
}

// 向L2日志缓冲区添加日志
inline void append_l2_log(const char* format, ...) {
    // 检查剩余空间
    if (g_l2_log_buffer_pos >= LOG_BUFFER_THRESHOLD) {
        // 缓冲区接近满，先打印
        strategy_log(0, g_l2_log_buffer);
        g_l2_log_buffer_pos = 0;  // 重置缓冲区位置
        std::cout << "Buffer threshold reached: " << LOG_BUFFER_THRESHOLD << " bytes, flushed after " 
                  << g_log_count << " logs" << std::endl;
        g_log_count = 0;
    }
    
    va_list args;
    va_start(args, format);
    // 直接写入缓冲区，避免中间字符串分配
    int written = vsnprintf(g_l2_log_buffer + g_l2_log_buffer_pos, 
                           MAX_LOG_BUFFER_SIZE - g_l2_log_buffer_pos,
                           format, args);
    va_end(args);
    
    if (written > 0) {
        g_l2_log_buffer_pos += written;
        g_log_count++;
        
        // 添加换行符
        if (g_l2_log_buffer_pos < MAX_LOG_BUFFER_SIZE - 1) {
            g_l2_log_buffer[g_l2_log_buffer_pos++] = '\n';
            g_l2_log_buffer[g_l2_log_buffer_pos] = '\0';
        }
    }
}

// 强制刷新L2日志缓冲区
inline void flush_l2_log_buffer(bool force = false) {
    // 获取当前时间
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int64_t current_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;  // 毫秒
    
    // 如果有内容且(强制刷新或达到阈值或距离上次刷新超过1秒)
    if (g_l2_log_buffer_pos > 0 && 
        (force || 
         g_l2_log_buffer_pos >= LOG_BUFFER_THRESHOLD || 
         current_time - g_last_flush_time > 1000)) {
        strategy_log(0, g_l2_log_buffer);
        std::cout << "Buffer flushed: " << g_l2_log_buffer_pos << " bytes, " 
                  << g_log_count << " logs, " 
                  << (force ? "forced" : (g_l2_log_buffer_pos >= LOG_BUFFER_THRESHOLD ? "threshold" : "timeout")) 
                  << std::endl;
        g_l2_log_buffer_pos = 0;  // 重置缓冲区位置
        g_last_flush_time = current_time;
        g_log_count = 0;
    }
}

// 生成测试用的逐笔委托数据
void generate_entrust_data(TickByTickData& tbt, const char* symbol, char side, char ord_type, int64_t price, int64_t qty) {
    memset(&tbt, 0, sizeof(TickByTickData));
    strncpy(tbt.symbol, symbol, sizeof(tbt.symbol) - 1);
    tbt.type = '0';  // 逐笔委托
    
    struct timeval tv;
    gettimeofday(&tv, NULL);
    tbt.data_time = (tv.tv_sec % 86400) * 1000000 + tv.tv_usec;  // 模拟交易所时间
    
    tbt.data.entrust.side = side;
    tbt.data.entrust.ord_type = ord_type;
    tbt.data.entrust.price = price;
    tbt.data.entrust.qty = qty;
    tbt.data.entrust.order_no = rand() % 10000000;  // 随机委托号
}

// 生成测试用的逐笔成交数据
void generate_trade_data(TickByTickData& tbt, const char* symbol, char trade_flag, int64_t price, int64_t qty) {
    memset(&tbt, 0, sizeof(TickByTickData));
    strncpy(tbt.symbol, symbol, sizeof(tbt.symbol) - 1);
    tbt.type = '1';  // 逐笔成交
    
    struct timeval tv;
    gettimeofday(&tv, NULL);
    tbt.data_time = (tv.tv_sec % 86400) * 1000000 + tv.tv_usec;  // 模拟交易所时间
    
    tbt.data.trade.trade_flag = trade_flag;
    tbt.data.trade.price = price;
    tbt.data.trade.qty = qty;
    tbt.data.trade.bid_no = rand() % 10000000;  // 随机买方委托号
    tbt.data.trade.ask_no = rand() % 10000000;  // 随机卖方委托号
}

// 模拟处理逐笔数据并记录日志
void process_tickbytick(const TickByTickData& tbt) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    
    // 模拟处理逐笔委托
    if (tbt.type == '0') {
        const auto& entrust = tbt.data.entrust;
        const char* order_action = entrust.ord_type == 'A' ? "BW" : "CW";
        
        // 模拟上海市场涨停价委托
        if (entrust.side == '1' && entrust.qty > 10000) {
            append_l2_log("%s|%s|V:%lld|T:%lld|N:%lld|LT:%ld.%06ld", 
                         tbt.symbol, order_action, (long long)entrust.qty, tbt.data_time, 
                         (long long)entrust.order_no, (long)tv.tv_sec, (long)tv.tv_usec);
        }
    } 
    // 模拟处理逐笔成交
    else if (tbt.type == '1') {
        const auto& trade = tbt.data.trade;
        
        // 模拟上海市场涨停价主动买入成交
        if (trade.trade_flag == 'B') {
            append_l2_log("%s|ZB|V:%lld|T:%lld|BN:%lld|SN:%lld|LT:%ld.%06ld",
                         tbt.symbol, (long long)trade.qty, tbt.data_time, 
                         (long long)trade.bid_no, (long long)trade.ask_no,
                         (long)tv.tv_sec, (long)tv.tv_usec);
        }
        // 模拟深圳市场撤单成交
        else if (trade.trade_flag == '4') {
            append_l2_log("%s|CW|V:%lld|T:%lld|N:%lld|LT:%ld.%06ld",
                         tbt.symbol, (long long)trade.qty, tbt.data_time, 
                         (long long)trade.seq, (long)tv.tv_sec, (long)tv.tv_usec);
        }
    }
    

}

// 性能测试函数
void performance_test(int num_messages, bool high_frequency = false) {
    std::cout << "Starting performance test with " << num_messages << " messages, " 
              << (high_frequency ? "high" : "normal") << " frequency" << std::endl;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 生成并处理大量逐笔数据
    for (int i = 0; i < num_messages; i++) {
        TickByTickData tbt;
        
        if (i % 2 == 0) {
            // 生成委托数据
            generate_entrust_data(tbt, "SH.600000", '1', 'A', 1000000, 20000);
        } else {
            // 生成成交数据
            generate_trade_data(tbt, "SZ.000001", 'B', 1000000, 15000);
        }
        
        // 处理数据
        process_tickbytick(tbt);
        
        // 高频模式下不延迟，模拟极端情况
        if (!high_frequency && i % 10 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    // 确保所有日志都被刷新
    flush_l2_log_buffer(true);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    
    std::cout << "Performance test completed in " << duration << " ms" << std::endl;
    std::cout << "Average processing time: " << (double)duration / num_messages << " ms per message" << std::endl;
    std::cout << "Message rate: " << (num_messages * 1000.0 / duration) << " messages per second" << std::endl;
    std::cout << "Total flushes: " << g_flush_count << std::endl;
    std::cout << "Average messages per flush: " << (double)num_messages / g_flush_count << std::endl;
}

// 测试不同大小的日志消息
void test_different_message_sizes() {
    std::cout << "\n=== Testing Different Message Sizes ===" << std::endl;
    
    // 重置计数器
    g_flush_count = 0;
    g_log_count = 0;
    g_l2_log_buffer_pos = 0;
    
    // 短消息
    for (int i = 0; i < 1000; i++) {
        append_l2_log("SH.600000|BW|V:20000|T:95128980|N:12345|LT:1634567890.123456");
    }
    flush_l2_log_buffer(true);
    std::cout << "Short messages test completed" << std::endl;
    
    // 重置计数器
    g_flush_count = 0;
    g_log_count = 0;
    g_l2_log_buffer_pos = 0;
    
    // 长消息
    for (int i = 0; i < 500; i++) {
        append_l2_log("SH.600000|BW|V:20000|T:95128980|N:12345|LT:1634567890.123456|EXTRA_DATA:This_is_a_much_longer_message_with_additional_information_to_test_buffer_capacity_with_longer_strings");
    }
    flush_l2_log_buffer(true);
    std::cout << "Long messages test completed" << std::endl;
}

// 测试时间触发的刷新
void test_time_based_flush() {
    std::cout << "\n=== Testing Time-Based Flush ===" << std::endl;
    
    // 重置计数器
    g_flush_count = 0;
    g_log_count = 0;
    g_l2_log_buffer_pos = 0;
    g_last_flush_time = 0;
    
    // 添加少量日志，不足以触发阈值刷新
    for (int i = 0; i < 100; i++) {
        append_l2_log("SH.600000|BW|V:20000|T:95128980|N:12345|LT:1634567890.123456");
    }
    
    std::cout << "Added 100 messages, waiting for time-based flush..." << std::endl;
    
    // 等待2秒，应该触发基于时间的刷新
    std::this_thread::sleep_for(std::chrono::seconds(2));
    flush_l2_log_buffer(false);
    
    std::cout << "Time-based flush test completed" << std::endl;
}

int main() {
    std::cout << "=== Log Buffer System Test ===" << std::endl;
    std::cout << "Buffer size: " << MAX_LOG_BUFFER_SIZE << " bytes" << std::endl;
    std::cout << "Flush threshold: " << LOG_BUFFER_THRESHOLD << " bytes" << std::endl;
    
    // 测试1: 正常频率下的性能测试
    performance_test(10000, false);
    
    // 测试2: 高频场景下的性能测试
    performance_test(10000, true);
    
    // 测试3: 不同大小的消息
    test_different_message_sizes();
    
    // 测试4: 基于时间的刷新
    test_time_based_flush();
    
    std::cout << "\nAll tests completed successfully!" << std::endl;
    return 0;
} 