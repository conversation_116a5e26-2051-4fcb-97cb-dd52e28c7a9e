#include <iostream>
#include <string>
#include <vector>
#include <array>
#include <chrono>
#include <random>
#include <cstring>
#include <unordered_map>
#include <algorithm>

#ifdef _WIN32
#include <intrin.h>
#include <windows.h>
#else
#include <x86intrin.h>
#endif

using namespace std;

// 股票代码大小
constexpr size_t SYMBOL_SIZE = 12;

// 测试参数
constexpr size_t MAX_SYMBOLS = 64;        // 最大股票数量
constexpr size_t HASH_TABLE_SIZE = 128;   // 哈希表大小
constexpr size_t TEST_ITERATIONS = 1000000; // 测试迭代次数

// 股票代码结构
struct SymbolEntry {
    char symbol[SYMBOL_SIZE];
    bool is_active;

    SymbolEntry() : is_active(false) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 全局数据
static SymbolEntry g_symbol_table[MAX_SYMBOLS];
static size_t g_symbol_count = 0;
static uint64_t g_symbol_bitmap = 0;  // 位图
static int g_symbol_hash_table[HASH_TABLE_SIZE];
static unordered_map<string, int> g_symbol_map; // STL哈希表

// 高性能股票代码比较函数
inline bool is_symbol_equal(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t* sym1_64 = reinterpret_cast<const uint64_t*>(symbol1);
    const uint64_t* sym2_64 = reinterpret_cast<const uint64_t*>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t* sym1_32 = reinterpret_cast<const uint32_t*>(symbol1 + 8);
    const uint32_t* sym2_32 = reinterpret_cast<const uint32_t*>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}

// 高性能股票代码比较函数 SH.991233
inline bool is_symbol_equal2(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t* sym1_64 = reinterpret_cast<const uint64_t*>(symbol1+1);
    const uint64_t* sym2_64 = reinterpret_cast<const uint64_t*>(symbol2+1);

    // 比较前8字节
    return sym1_64[0] == sym2_64[0];
    // 比较后4字节
}


// SIMD优化的股票代码比较函数
inline bool is_symbol_equal_simd(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
#ifdef __SSE2__
    // 使用SSE2指令集优化比较
    __m128i a = _mm_loadu_si128(reinterpret_cast<const __m128i*>(symbol1));
    __m128i b = _mm_loadu_si128(reinterpret_cast<const __m128i*>(symbol2));

    // 比较整个12字节，生成掩码
    __m128i cmp = _mm_cmpeq_epi8(a, b);

    // 检查前12字节是否全部匹配
    int mask = _mm_movemask_epi8(cmp);
    return (mask & 0xFFF) == 0xFFF;
#else
    return is_symbol_equal(symbol1, symbol2);
#endif
}

// 哈希函数
inline uint32_t hash_symbol(const char symbol[SYMBOL_SIZE]) {
    // FNV-1a 哈希算法，针对12字节的股票代码优化
    uint32_t hash = 2166136261u; // FNV偏移基数
    for (int i = 0; i < SYMBOL_SIZE && symbol[i]; ++i) {
        hash ^= static_cast<uint32_t>(symbol[i]);
        hash *= 16777619u; // FNV素数
    }
    return hash;
}

// 初始化哈希表
void init_hash_table() {
    // 将所有项初始化为-1（表示空槽位）
    for (size_t i = 0; i < HASH_TABLE_SIZE; ++i) {
        g_symbol_hash_table[i] = -1;
    }
}

// 向哈希表中添加股票代码
void add_to_hash_table(const char symbol[SYMBOL_SIZE], int index) {
    uint32_t hash = hash_symbol(symbol);
    size_t slot = hash & (HASH_TABLE_SIZE - 1); // 掩码操作，等同于 hash % HASH_TABLE_SIZE

    // 线性探测解决冲突
    while (g_symbol_hash_table[slot] != -1) {
        slot = (slot + 1) & (HASH_TABLE_SIZE - 1);
    }

    g_symbol_hash_table[slot] = index;
}

// 设置位图状态
inline void set_bitmap(int index, bool active) {
    if (index < 0 || index >= 64) return;
    if (active) {
        g_symbol_bitmap |= (1ULL << index);
    } else {
        g_symbol_bitmap &= ~(1ULL << index);
    }
}

// 检查位图状态
inline bool check_bitmap(int index) {
    if (index < 0 || index >= 64) return false;
    return (g_symbol_bitmap & (1ULL << index)) != 0;
}

// 方法1: 数组线性查找
int find_by_array(const char symbol[SYMBOL_SIZE]) {
    for (size_t i = 0; i < g_symbol_count; ++i) {
        if (g_symbol_table[i].is_active && is_symbol_equal(g_symbol_table[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// 方法1b: 数组线性查找 (SIMD优化)
int find_by_array_simd(const char symbol[SYMBOL_SIZE]) {
    for (size_t i = 0; i < g_symbol_count; ++i) {
        if (g_symbol_table[i].is_active && is_symbol_equal2(g_symbol_table[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// 方法2: 哈希表查找
int find_by_hash(const char symbol[SYMBOL_SIZE]) {
    uint32_t hash = hash_symbol(symbol);
    size_t slot = hash & (HASH_TABLE_SIZE - 1);

    // 查找匹配项
    for (size_t i = 0; i < HASH_TABLE_SIZE; ++i) {
        size_t current_slot = (slot + i) & (HASH_TABLE_SIZE - 1);
        int index = g_symbol_hash_table[current_slot];

        if (index == -1) break; // 空槽位，未找到

        if (index >= 0 && index < MAX_SYMBOLS &&
            g_symbol_table[index].is_active &&
            is_symbol_equal(g_symbol_table[index].symbol, symbol)) {
            return index;
        }
    }

    return -1;
}

// 方法3: STL unordered_map查找
int find_by_stl_map(const char symbol[SYMBOL_SIZE]) {
    string sym_str(symbol);
    auto it = g_symbol_map.find(sym_str);
    if (it != g_symbol_map.end()) {
        int index = it->second;
        if (index >= 0 && index < MAX_SYMBOLS && g_symbol_table[index].is_active) {
            return index;
        }
    }
    return -1;
}

// 方法4: 位图+数组查找
int find_by_bitmap_array(const char symbol[SYMBOL_SIZE]) {
    for (size_t i = 0; i < g_symbol_count; ++i) {
        if (check_bitmap(i) && is_symbol_equal(g_symbol_table[i].symbol, symbol)) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// 方法5: 位图过滤 + 哈希表查找（组合方法）
int find_by_bitmap_hash(const char symbol[SYMBOL_SIZE]) {
    // 先检查位图是否有任何活跃股票
    if (g_symbol_bitmap == 0) return -1;

    // 然后使用哈希表查找
    return find_by_hash(symbol);
}

// 生成随机股票代码
void generate_random_symbol(array<char, SYMBOL_SIZE>& symbol) {
    static const char charset[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.";
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dist(0, sizeof(charset) - 2);

    // 格式: XX.XXXXXX (市场.代码)
    symbol[0] = charset[dist(gen) % 26]; // 字母
    symbol[1] = charset[dist(gen) % 26]; // 字母
    symbol[2] = '.';
    for (int i = 3; i < 9; ++i) {
        symbol[i] = charset[dist(gen) % 10 + 26]; // 数字
    }
    symbol[9] = '\0';

    // 确保剩余字节为0
    for (int i = 10; i < SYMBOL_SIZE; ++i) {
        symbol[i] = '\0';
    }
}

// 初始化测试数据
void init_test_data(size_t num_symbols) {
    // 清空数据
    g_symbol_count = 0;
    g_symbol_bitmap = 0;
    init_hash_table();
    g_symbol_map.clear();

    // 生成随机股票代码
    for (size_t i = 0; i < num_symbols; ++i) {
        array<char, SYMBOL_SIZE> temp_symbol;
        generate_random_symbol(temp_symbol);
        memcpy(g_symbol_table[i].symbol, temp_symbol.data(), SYMBOL_SIZE);
        g_symbol_table[i].is_active = true;

        // 添加到哈希表
        add_to_hash_table(g_symbol_table[i].symbol, i);

        // 添加到STL map
        g_symbol_map[g_symbol_table[i].symbol] = i;

        // 设置位图
        set_bitmap(i, true);
    }

    g_symbol_count = num_symbols;
}

// 运行性能测试
void run_performance_test(size_t num_symbols) {
    cout << "======= 测试 " << num_symbols << " 只股票 =======" << endl;

    // 初始化测试数据
    init_test_data(num_symbols);

    // 准备测试样本
    vector<array<char, SYMBOL_SIZE>> test_samples(TEST_ITERATIONS);

    // 生成测试样本 - 80%命中，20%未命中
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        if (i % 5 == 0) {
            // 未命中样本
            generate_random_symbol(test_samples[i]);
        } else {
            // 命中样本 - 随机选择已有股票
            size_t idx = i % num_symbols;
            memcpy(test_samples[i].data(), g_symbol_table[idx].symbol, SYMBOL_SIZE);
        }
    }

    // 测试方法1: 数组线性查找
    auto start = chrono::high_resolution_clock::now();
    int hits1 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_array(test_samples[i].data());
        if (result >= 0) hits1++;
    }
    auto end = chrono::high_resolution_clock::now();
    double time1 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法1b: 数组线性查找 (SIMD优化)
    start = chrono::high_resolution_clock::now();
    int hits1b = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_array_simd(test_samples[i].data());
        if (result >= 0) hits1b++;
    }
    end = chrono::high_resolution_clock::now();
    double time1b = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法2: 哈希表查找
    start = chrono::high_resolution_clock::now();
    int hits2 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_hash(test_samples[i].data());
        if (result >= 0) hits2++;
    }
    end = chrono::high_resolution_clock::now();
    double time2 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法3: STL unordered_map查找
    start = chrono::high_resolution_clock::now();
    int hits3 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_stl_map(test_samples[i].data());
        if (result >= 0) hits3++;
    }
    end = chrono::high_resolution_clock::now();
    double time3 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法4: 位图+数组查找
    start = chrono::high_resolution_clock::now();
    int hits4 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_bitmap_array(test_samples[i].data());
        if (result >= 0) hits4++;
    }
    end = chrono::high_resolution_clock::now();
    double time4 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 测试方法5: 位图+哈希表查找
    start = chrono::high_resolution_clock::now();
    int hits5 = 0;
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        int result = find_by_bitmap_hash(test_samples[i].data());
        if (result >= 0) hits5++;
    }
    end = chrono::high_resolution_clock::now();
    double time5 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

    // 输出结果
    cout << "数组线性查找:      " << time1 << " ns/op, 命中: " << hits1 << endl;
    cout << "数组线性查找(SIMD): " << time1b << " ns/op, 命中: " << hits1b << endl;
    cout << "哈希表查找:        " << time2 << " ns/op, 命中: " << hits2 << endl;
    cout << "STL unordered_map: " << time3 << " ns/op, 命中: " << hits3 << endl;
    cout << "位图+数组查找:     " << time4 << " ns/op, 命中: " << hits4 << endl;
    cout << "位图+哈希表查找:   " << time5 << " ns/op, 命中: " << hits5 << endl;
    cout << endl;

    // 验证所有方法返回相同结果
    if (hits1 != hits2 || hits1 != hits3 || hits1 != hits4 || hits1 != hits5) {
        cout << "警告: 不同方法返回的结果不一致!" << endl;
    }
}

// 测试涨幅过滤的效果
void run_filter_test() {
    cout << "======= 测试涨幅过滤效果 =======" << endl;

    // 初始化64只股票
    init_test_data(64);

    // 模拟不同涨幅比例的场景
    vector<double> watching_ratios = {0.0, 0.05, 0.1, 0.2, 0.5, 0.8, 1.0};

    for (double ratio : watching_ratios) {
        // 重置位图
        g_symbol_bitmap = 0;

        // 设置一定比例的股票为"达到涨幅阈值"状态
        size_t watching_count = static_cast<size_t>(g_symbol_count * ratio);
        for (size_t i = 0; i < watching_count; ++i) {
            set_bitmap(i, true);
        }

        // 准备测试样本 - 均匀分布在所有股票中
        vector<array<char, SYMBOL_SIZE>> test_samples(TEST_ITERATIONS);
        for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
            size_t idx = i % g_symbol_count;
            memcpy(test_samples[i].data(), g_symbol_table[idx].symbol, SYMBOL_SIZE);
        }

        // 测试方法1: 不使用涨幅过滤的数组查找
        auto start = chrono::high_resolution_clock::now();
        int hits1 = 0;
        for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
            int result = find_by_array(test_samples[i].data());
            if (result >= 0) hits1++;
        }
        auto end = chrono::high_resolution_clock::now();
        double time1 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

        // 测试方法2: 使用涨幅过滤的位图+数组查找
        start = chrono::high_resolution_clock::now();
        int hits2 = 0;
        for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
            int result = find_by_bitmap_array(test_samples[i].data());
            if (result >= 0) hits2++;
        }
        end = chrono::high_resolution_clock::now();
        double time2 = chrono::duration<double, std::nano>(end - start).count() / TEST_ITERATIONS;

        // 输出结果
        cout << "涨幅比例 " << (ratio * 100) << "%:" << endl;
        cout << "  不使用过滤: " << time1 << " ns/op, 命中: " << hits1 << endl;
        cout << "  使用位图过滤: " << time2 << " ns/op, 命中: " << hits2 << endl;
        cout << "  加速比: " << (time1 / time2) << "x" << endl;
        cout << endl;
    }
}

int main() {
    // 设置控制台编码为UTF-8，解决中文乱码问题
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
#endif

    cout << "性能测试: 比较不同股票代码查找方法" << endl;
    cout << "测试迭代次数: " << TEST_ITERATIONS << endl << endl;

    // 测试不同数量的股票
    run_performance_test(1);   // 1只股票
    run_performance_test(2);   // 2只股票
    run_performance_test(4);   // 4只股票
    run_performance_test(8);   // 8只股票
    run_performance_test(16);  // 16只股票
    run_performance_test(32);  // 32只股票
    run_performance_test(64);  // 64只股票

    // 测试涨幅过滤效果
    run_filter_test();

    cout << "按任意键退出..." << endl;
    getchar();
    
    return 0;
} 