#include <iostream>
#include <chrono>
#include <random>
#include <vector>
#include <cstring>
#include <algorithm>

#ifdef _WIN32
#include <intrin.h>
#include <windows.h>
#else
#include <x86intrin.h>
#endif

using namespace std;

// 测试参数
constexpr size_t MAX_SYMBOLS = 14;        // 最大股票数量
constexpr size_t TEST_ITERATIONS = 10000000; // 测试迭代次数
constexpr double ACTIVE_RATIO = 0.3;      // 激活比例（30%的股票处于激活状态）

// 模拟股票结构体
struct StockEntry {
    bool is_active;     // 是否激活
    bool is_watching;   // 是否监控
    uint64_t price;     // 价格
    uint64_t volume;    // 成交量
    
    StockEntry() : is_active(false), is_watching(false), price(0), volume(0) {}
};

// 全局测试数据
static StockEntry g_stocks[MAX_SYMBOLS];
static uint64_t g_active_bitmap = 0;      // 激活状态位图
static uint64_t g_watching_bitmap = 0;    // 监控状态位图

// 位图操作函数
inline bool is_stock_active_bitmap(int index) {
    if (index < 0 || index >= 64) return false;
    return (g_active_bitmap & (1ULL << index)) != 0;
}

inline bool is_stock_watching_bitmap(int index) {
    if (index < 0 || index >= 64) return false;
    return (g_watching_bitmap & (1ULL << index)) != 0;
}

inline void set_stock_active_bitmap(int index, bool active) {
    if (index < 0 || index >= 64) return;
    if (active) {
        g_active_bitmap |= (1ULL << index);
    } else {
        g_active_bitmap &= ~(1ULL << index);
    }
}

inline void set_stock_watching_bitmap(int index, bool watching) {
    if (index < 0 || index >= 64) return;
    if (watching) {
        g_watching_bitmap |= (1ULL << index);
    } else {
        g_watching_bitmap &= ~(1ULL << index);
    }
}

// 初始化测试数据
void init_test_data() {
    // 随机数生成器
    random_device rd;
    mt19937 gen(rd());
    uniform_real_distribution<> dis(0.0, 1.0);
    
    // 初始化股票数据
    for (int i = 0; i < MAX_SYMBOLS; ++i) {
        bool active = (dis(gen) < ACTIVE_RATIO);
        bool watching = active && (dis(gen) < 0.5); // 50%的激活股票处于监控状态
        
        // 设置结构体
        g_stocks[i].is_active = active;
        g_stocks[i].is_watching = watching;
        g_stocks[i].price = 100000 + i * 100; // 模拟价格
        g_stocks[i].volume = 10000 + i * 1000; // 模拟成交量
        
        // 设置位图
        set_stock_active_bitmap(i, active);
        set_stock_watching_bitmap(i, watching);
    }
}

// 生成随机索引序列用于测试
vector<int> generate_random_indices(size_t count) {
    vector<int> indices(count);
    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> dis(0, MAX_SYMBOLS - 1);
    
    for (size_t i = 0; i < count; ++i) {
        indices[i] = dis(gen);
    }
    
    return indices;
}

// 测试方法1：直接访问结构体成员
uint64_t test_struct_member_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        if (g_stocks[idx].is_active) {
            sum += g_stocks[idx].price;
            
            if (g_stocks[idx].is_watching) {
                sum += g_stocks[idx].volume;
            }
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "结构体成员访问: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试方法2：使用位图
uint64_t test_bitmap_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        if (is_stock_active_bitmap(idx)) {
            sum += g_stocks[idx].price;
            
            if (is_stock_watching_bitmap(idx)) {
                sum += g_stocks[idx].volume;
            }
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "位图访问: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试方法3：内联位图操作（不使用函数调用）
uint64_t test_inline_bitmap_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        if ((g_active_bitmap & (1ULL << idx)) != 0) {
            sum += g_stocks[idx].price;
            
            if ((g_watching_bitmap & (1ULL << idx)) != 0) {
                sum += g_stocks[idx].volume;
            }
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "内联位图操作: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试方法4：预取优化的结构体访问
uint64_t test_prefetch_struct_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 预取下一个元素到缓存
        if (i + 1 < indices.size()) {
            __builtin_prefetch(&g_stocks[indices[i + 1]], 0, 3);
        }
        
        if (g_stocks[idx].is_active) {
            sum += g_stocks[idx].price;
            
            if (g_stocks[idx].is_watching) {
                sum += g_stocks[idx].volume;
            }
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "预取优化的结构体访问: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试方法5：预取优化的位图访问
uint64_t test_prefetch_bitmap_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 预取下一个元素到缓存
        if (i + 1 < indices.size()) {
            __builtin_prefetch(&g_stocks[indices[i + 1]], 0, 3);
        }
        
        if ((g_active_bitmap & (1ULL << idx)) != 0) {
            sum += g_stocks[idx].price;
            
            if ((g_watching_bitmap & (1ULL << idx)) != 0) {
                sum += g_stocks[idx].volume;
            }
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "预取优化的位图访问: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试方法6：批量位图检查
uint64_t test_batch_bitmap_access(const vector<int>& indices) {
    uint64_t sum = 0;
    
    // 将索引分组，每64个一组
    vector<uint64_t> batches;
    for (size_t i = 0; i < indices.size(); i += 64) {
        uint64_t batch = 0;
        for (size_t j = 0; j < 64 && i + j < indices.size(); ++j) {
            int idx = indices[i + j];
            if (idx >= 0 && idx < 64) {
                batch |= (1ULL << j);
            }
        }
        batches.push_back(batch);
    }
    
    auto start = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < batches.size(); ++i) {
        uint64_t active_mask = batches[i] & g_active_bitmap;
        
        while (active_mask) {
            // 获取最低位的1的位置
            int pos = __builtin_ctzll(active_mask);
            int idx = indices[i * 64 + pos];
            
            sum += g_stocks[idx].price;
            
            if ((g_watching_bitmap & (1ULL << idx)) != 0) {
                sum += g_stocks[idx].volume;
            }
            
            // 清除已处理的位
            active_mask &= (active_mask - 1);
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    double time = chrono::duration<double, std::nano>(end - start).count();
    
    cout << "批量位图检查: " << time << " ns 总计, " 
         << (time / indices.size()) << " ns/op, 结果: " << sum << endl;
    
    return sum;
}

// 测试顺序访问和随机访问的性能差异
void test_access_patterns() {
    cout << "===== 测试顺序访问和随机访问的性能差异 =====" << endl;
    
    // 顺序访问
    vector<int> sequential_indices(TEST_ITERATIONS);
    for (size_t i = 0; i < TEST_ITERATIONS; ++i) {
        sequential_indices[i] = i % MAX_SYMBOLS;
    }
    
    cout << "顺序访问模式:" << endl;
    uint64_t sum1 = test_struct_member_access(sequential_indices);
    uint64_t sum2 = test_bitmap_access(sequential_indices);
    uint64_t sum3 = test_inline_bitmap_access(sequential_indices);
    
    // 随机访问
    vector<int> random_indices = generate_random_indices(TEST_ITERATIONS);
    
    cout << "\n随机访问模式:" << endl;
    uint64_t sum4 = test_struct_member_access(random_indices);
    uint64_t sum5 = test_bitmap_access(random_indices);
    uint64_t sum6 = test_inline_bitmap_access(random_indices);
    
    // 验证结果一致性
    if (sum1 != sum2 || sum1 != sum3 || sum4 != sum5 || sum4 != sum6) {
        cout << "警告: 不同方法的结果不一致!" << endl;
    }
}

// 测试预取优化的效果
void test_prefetch_optimization() {
    cout << "\n===== 测试预取优化的效果 =====" << endl;
    
    // 随机访问
    vector<int> random_indices = generate_random_indices(TEST_ITERATIONS);
    
    uint64_t sum1 = test_struct_member_access(random_indices);
    uint64_t sum2 = test_prefetch_struct_access(random_indices);
    uint64_t sum3 = test_bitmap_access(random_indices);
    uint64_t sum4 = test_prefetch_bitmap_access(random_indices);
    uint64_t sum5 = test_batch_bitmap_access(random_indices);
    
    // 验证结果一致性
    if (sum1 != sum2 || sum1 != sum3 || sum1 != sum4 || sum1 != sum5) {
        cout << "警告: 不同方法的结果不一致!" << endl;
    }
}

// 测试不同激活比例下的性能
void test_different_active_ratios() {
    cout << "\n===== 测试不同激活比例下的性能 =====" << endl;
    
    vector<double> ratios = {0.01, 0.05, 0.1, 0.3, 0.5, 0.8};
    
    for (double ratio : ratios) {
        init_test_data();
        
        cout << "\n激活比例: " << (ratio * 100) << "%" << endl;
        
        // 随机访问
        vector<int> random_indices = generate_random_indices(TEST_ITERATIONS);
        
        uint64_t sum1 = test_struct_member_access(random_indices);
        uint64_t sum2 = test_bitmap_access(random_indices);
        uint64_t sum3 = test_inline_bitmap_access(random_indices);
        
        // 验证结果一致性
        if (sum1 != sum2 || sum1 != sum3) {
            cout << "警告: 不同方法的结果不一致!" << endl;
        }
    }
}

int main() {
    // 设置控制台编码为UTF-8，解决中文乱码问题
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
#endif

    cout << "位图访问 vs 结构体成员访问性能测试" << endl;
    cout << "测试迭代次数: " << TEST_ITERATIONS << endl;
    cout << "最大股票数量: " << MAX_SYMBOLS << endl;
    cout << "激活比例: " << (ACTIVE_RATIO * 100) << "%" << endl << endl;
    
    // 初始化测试数据
    init_test_data();
    
    // 测试顺序访问和随机访问的性能差异
    test_access_patterns();
    
    // 测试预取优化的效果
    test_prefetch_optimization();
    
    // 测试不同激活比例下的性能
    // test_different_active_ratios();
    
    cout << "\n按任意键退出..." << endl;
    getchar();
    
    return 0;
} 