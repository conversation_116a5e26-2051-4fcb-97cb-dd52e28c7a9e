#include <iostream>
#include <chrono>
#include <random>
#include <vector>
#include <cstring>
#include <algorithm>
#include <numeric>
#include <iomanip>

#ifdef _WIN32
#include <intrin.h>
#include <windows.h>
#define PREFETCH(addr) _mm_prefetch((const char*)(addr), _MM_HINT_T0)
#else
#include <x86intrin.h>
#define PREFETCH(addr) __builtin_prefetch((addr), 0, 3)
#endif

using namespace std;

// 测试参数
constexpr size_t MAX_SYMBOLS = 64;        // 最大股票数量
constexpr size_t TEST_ITERATIONS = 10000000; // 测试迭代次数
constexpr size_t WARM_UP_ITERATIONS = 1000000; // 预热迭代次数
constexpr size_t STRUCT_SIZE = 64;        // 结构体大小（缓存行对齐）

// 模拟股票结构体 - 缓存行对齐
struct alignas(64) StockData {
    char symbol[12];            // 股票代码
    int market_type;            // 市场类型
    bool is_active;             // 是否激活
    bool is_ordered;            // 是否已下单
    uint64_t price;             // 价格
    uint64_t volume;            // 成交量
    uint64_t threshold;         // 阈值
    uint64_t timestamp;         // 时间戳
    uint8_t padding[23];        // 填充至64字节
    
    StockData() : market_type(0), is_active(false), is_ordered(false), 
                  price(0), volume(0), threshold(0), timestamp(0) {
        memset(symbol, 0, sizeof(symbol));
        memset(padding, 0, sizeof(padding));
    }
};

// 模拟实时数据结构体 - 缓存行对齐
struct alignas(64) RealTimeData {
    int64_t base_volume;        // 基准成交量
    int64_t consumed_volume;    // 已消耗成交量
    int64_t current_volume;     // 当前成交量
    int64_t order_time;         // 下单时间
    bool is_limit_up;           // 是否涨停
    char order_id[32];          // 订单号
    uint8_t padding[15];        // 填充至64字节
    
    RealTimeData() : base_volume(0), consumed_volume(0), current_volume(0),
                     order_time(0), is_limit_up(false) {
        memset(order_id, 0, sizeof(order_id));
        memset(padding, 0, sizeof(padding));
    }
};

// 模拟紧凑数组
struct CompactArray {
    int indices[MAX_SYMBOLS];  // 存储原始数组的索引
    int count;                 // 当前元素数量
    
    CompactArray() : count(0) {
        memset(indices, -1, sizeof(indices));
    }
    
    void clear() {
        count = 0;
        memset(indices, -1, sizeof(indices));
    }
    
    void add(int index) {
        if (count < MAX_SYMBOLS && index >= 0) {
            indices[count++] = index;
        }
    }
};

// 全局测试数据
static StockData g_stock_data[MAX_SYMBOLS];
static RealTimeData g_realtime_data[MAX_SYMBOLS];
static CompactArray g_compact_array;

// 初始化测试数据
void init_test_data(int active_count) {
    // 随机数生成器
    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> price_dist(100000, 500000);
    uniform_int_distribution<> volume_dist(10000, 1000000);
    
    // 初始化股票数据
    for (int i = 0; i < MAX_SYMBOLS; ++i) {
        g_stock_data[i].is_active = (i < active_count);
        g_stock_data[i].market_type = (i % 2);  // 交替设置市场类型
        g_stock_data[i].price = price_dist(gen);
        g_stock_data[i].volume = volume_dist(gen);
        g_stock_data[i].threshold = g_stock_data[i].price * 0.9;
        g_stock_data[i].timestamp = i * 1000;
        
        // 设置股票代码
        if (g_stock_data[i].market_type == 0) {
            snprintf(g_stock_data[i].symbol, sizeof(g_stock_data[i].symbol), "SH.%06d", 600000 + i);
        } else {
            snprintf(g_stock_data[i].symbol, sizeof(g_stock_data[i].symbol), "SZ.%06d", 000001 + i);
        }
        
        // 初始化实时数据
        g_realtime_data[i].base_volume = volume_dist(gen);
        g_realtime_data[i].current_volume = g_realtime_data[i].base_volume * 0.8;
        g_realtime_data[i].consumed_volume = g_realtime_data[i].base_volume * 0.2;
        g_realtime_data[i].is_limit_up = false;
    }
    
    // 初始化紧凑数组
    g_compact_array.clear();
    for (int i = 0; i < active_count; ++i) {
        g_compact_array.add(i);
    }
}

// 生成随机访问序列
vector<int> generate_access_pattern(size_t count, int pattern_type) {
    vector<int> indices(count);
    random_device rd;
    mt19937 gen(rd());
    
    switch (pattern_type) {
        case 0: // 完全随机访问
            {
                uniform_int_distribution<> dist(0, MAX_SYMBOLS - 1);
                for (size_t i = 0; i < count; ++i) {
                    indices[i] = dist(gen);
                }
            }
            break;
            
        case 1: // 局部性随机访问 - 80%的访问集中在20%的股票上
            {
                uniform_int_distribution<> hot_dist(0, MAX_SYMBOLS * 0.2 - 1);
                uniform_int_distribution<> cold_dist(MAX_SYMBOLS * 0.2, MAX_SYMBOLS - 1);
                uniform_real_distribution<> chance(0.0, 1.0);
                
                for (size_t i = 0; i < count; ++i) {
                    if (chance(gen) < 0.8) {
                        indices[i] = hot_dist(gen);  // 热点股票
                    } else {
                        indices[i] = cold_dist(gen);  // 冷门股票
                    }
                }
            }
            break;
            
        case 2: // 顺序访问
            for (size_t i = 0; i < count; ++i) {
                indices[i] = i % MAX_SYMBOLS;
            }
            break;
            
        case 3: // 紧凑数组访问模式 - 只访问紧凑数组中的股票
            {
                uniform_int_distribution<> dist(0, g_compact_array.count - 1);
                for (size_t i = 0; i < count; ++i) {
                    int compact_idx = dist(gen);
                    indices[i] = g_compact_array.indices[compact_idx];
                }
            }
            break;
    }
    
    return indices;
}

// 模拟处理函数 - 无预取版本
uint64_t process_without_prefetch(const vector<int>& indices) {
    uint64_t sum = 0;
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        if (g_stock_data[idx].is_active) {
            // 读取股票数据
            sum += g_stock_data[idx].price;
            
            // 读取实时数据
            if (g_realtime_data[idx].current_volume > g_stock_data[idx].threshold) {
                sum += g_realtime_data[idx].current_volume;
                
                // 模拟复杂计算
                if (!g_stock_data[idx].is_ordered && !g_realtime_data[idx].is_limit_up) {
                    sum += g_stock_data[idx].volume * g_stock_data[idx].price / 10000;
                }
            }
        }
    }
    
    return sum;
}

// 模拟处理函数 - 有预取版本
uint64_t process_with_prefetch(const vector<int>& indices) {
    uint64_t sum = 0;
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 预取下一个元素的数据
        if (i + 1 < indices.size()) {
            int next_idx = indices[i + 1];
            PREFETCH(&g_stock_data[next_idx]);
            PREFETCH(&g_realtime_data[next_idx]);
        }
        
        if (g_stock_data[idx].is_active) {
            // 读取股票数据
            sum += g_stock_data[idx].price;
            
            // 读取实时数据
            if (g_realtime_data[idx].current_volume > g_stock_data[idx].threshold) {
                sum += g_realtime_data[idx].current_volume;
                
                // 模拟复杂计算
                if (!g_stock_data[idx].is_ordered && !g_realtime_data[idx].is_limit_up) {
                    sum += g_stock_data[idx].volume * g_stock_data[idx].price / 10000;
                }
            }
        }
    }
    
    return sum;
}

// 模拟处理函数 - 预取当前元素版本
uint64_t process_with_current_prefetch(const vector<int>& indices) {
    uint64_t sum = 0;
    
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 预取当前元素的数据
        PREFETCH(&g_stock_data[idx]);
        PREFETCH(&g_realtime_data[idx]);
        
        if (g_stock_data[idx].is_active) {
            // 读取股票数据
            sum += g_stock_data[idx].price;
            
            // 读取实时数据
            if (g_realtime_data[idx].current_volume > g_stock_data[idx].threshold) {
                sum += g_realtime_data[idx].current_volume;
                
                // 模拟复杂计算
                if (!g_stock_data[idx].is_ordered && !g_realtime_data[idx].is_limit_up) {
                    sum += g_stock_data[idx].volume * g_stock_data[idx].price / 10000;
                }
            }
        }
    }
    
    return sum;
}

// 运行性能测试
void run_performance_test(const string& test_name, const vector<int>& indices) {
    cout << "===== " << test_name << " =====" << endl;
    
    // 预热
    for (size_t i = 0; i < WARM_UP_ITERATIONS; ++i) {
        int idx = indices[i % indices.size()];
        volatile uint64_t dummy = g_stock_data[idx].price + g_realtime_data[idx].current_volume;
    }
    
    // 测试无预取版本
    vector<double> times_no_prefetch;
    uint64_t sum1 = 0;
    
    for (int run = 0; run < 5; ++run) {
        auto start = chrono::high_resolution_clock::now();
        sum1 += process_without_prefetch(indices);
        auto end = chrono::high_resolution_clock::now();
        double time = chrono::duration<double, std::nano>(end - start).count();
        times_no_prefetch.push_back(time);
    }
    
    // 测试有预取版本（预取下一个元素）
    vector<double> times_with_prefetch;
    uint64_t sum2 = 0;
    
    for (int run = 0; run < 5; ++run) {
        auto start = chrono::high_resolution_clock::now();
        sum2 += process_with_prefetch(indices);
        auto end = chrono::high_resolution_clock::now();
        double time = chrono::duration<double, std::nano>(end - start).count();
        times_with_prefetch.push_back(time);
    }
    
    // 测试有预取版本（预取当前元素）
    vector<double> times_current_prefetch;
    uint64_t sum3 = 0;
    
    for (int run = 0; run < 5; ++run) {
        auto start = chrono::high_resolution_clock::now();
        sum3 += process_with_current_prefetch(indices);
        auto end = chrono::high_resolution_clock::now();
        double time = chrono::duration<double, std::nano>(end - start).count();
        times_current_prefetch.push_back(time);
    }
    
    // 计算统计数据
    sort(times_no_prefetch.begin(), times_no_prefetch.end());
    sort(times_with_prefetch.begin(), times_with_prefetch.end());
    sort(times_current_prefetch.begin(), times_current_prefetch.end());
    
    double avg_no_prefetch = accumulate(times_no_prefetch.begin(), times_no_prefetch.end(), 0.0) / times_no_prefetch.size();
    double avg_with_prefetch = accumulate(times_with_prefetch.begin(), times_with_prefetch.end(), 0.0) / times_with_prefetch.size();
    double avg_current_prefetch = accumulate(times_current_prefetch.begin(), times_current_prefetch.end(), 0.0) / times_current_prefetch.size();
    
    double median_no_prefetch = times_no_prefetch[times_no_prefetch.size() / 2];
    double median_with_prefetch = times_with_prefetch[times_with_prefetch.size() / 2];
    double median_current_prefetch = times_current_prefetch[times_current_prefetch.size() / 2];
    
    // 输出结果
    cout << fixed << setprecision(2);
    cout << "无预取:       总时间 = " << avg_no_prefetch << " ns, 平均 = " 
         << (avg_no_prefetch / indices.size()) << " ns/op, 中位数 = " 
         << (median_no_prefetch / indices.size()) << " ns/op" << endl;
    
    cout << "预取下一个:   总时间 = " << avg_with_prefetch << " ns, 平均 = " 
         << (avg_with_prefetch / indices.size()) << " ns/op, 中位数 = " 
         << (median_with_prefetch / indices.size()) << " ns/op" << endl;
    
    cout << "预取当前:     总时间 = " << avg_current_prefetch << " ns, 平均 = " 
         << (avg_current_prefetch / indices.size()) << " ns/op, 中位数 = " 
         << (median_current_prefetch / indices.size()) << " ns/op" << endl;
    
    double speedup1 = avg_no_prefetch / avg_with_prefetch;
    double speedup2 = avg_no_prefetch / avg_current_prefetch;
    
    cout << "预取下一个加速比: " << speedup1 << "x" << endl;
    cout << "预取当前加速比:   " << speedup2 << "x" << endl;
    
    // 验证结果一致性
    if (sum1 != sum2 || sum1 != sum3) {
        cout << "警告: 不同方法的结果不一致!" << endl;
    }
    
    cout << endl;
}

// 测试不同访问模式
void test_different_access_patterns() {
    const vector<string> pattern_names = {
        "完全随机访问",
        "局部性随机访问 (80%访问集中在20%的股票)",
        "顺序访问",
        "紧凑数组访问模式"
    };
    
    for (int i = 0; i < 4; ++i) {
        vector<int> indices = generate_access_pattern(TEST_ITERATIONS, i);
        run_performance_test(pattern_names[i], indices);
    }
}

// 测试不同活跃股票数量
void test_different_active_counts() {
    const vector<int> active_counts = {4, 8, 16, 32, 64};
    
    for (int active_count : active_counts) {
        init_test_data(active_count);
        
        ostringstream oss;
        oss << "活跃股票数量: " << active_count << " / " << MAX_SYMBOLS;
        
        vector<int> indices = generate_access_pattern(TEST_ITERATIONS, 1);  // 使用局部性随机访问模式
        run_performance_test(oss.str(), indices);
    }
}

// 测试缓存污染的影响
void test_cache_pollution() {
    init_test_data(16);  // 16个活跃股票
    
    cout << "===== 测试缓存污染的影响 =====" << endl;
    
    // 生成局部性随机访问模式
    vector<int> indices = generate_access_pattern(TEST_ITERATIONS, 1);
    
    // 分配一个大数组用于污染缓存
    const size_t POLLUTION_SIZE = 10 * 1024 * 1024;  // 10MB
    vector<char> pollution_data(POLLUTION_SIZE);
    
    // 测试无缓存污染
    uint64_t sum1 = 0;
    auto start1 = chrono::high_resolution_clock::now();
    sum1 = process_without_prefetch(indices);
    auto end1 = chrono::high_resolution_clock::now();
    double time1 = chrono::duration<double, std::nano>(end1 - start1).count();
    
    // 测试有缓存污染，无预取
    uint64_t sum2 = 0;
    auto start2 = chrono::high_resolution_clock::now();
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 每100次访问污染一次缓存
        if (i % 100 == 0) {
            for (size_t j = 0; j < POLLUTION_SIZE; j += 64) {
                pollution_data[j] = static_cast<char>(j);
            }
        }
        
        if (g_stock_data[idx].is_active) {
            sum2 += g_stock_data[idx].price;
            if (g_realtime_data[idx].current_volume > g_stock_data[idx].threshold) {
                sum2 += g_realtime_data[idx].current_volume;
            }
        }
    }
    auto end2 = chrono::high_resolution_clock::now();
    double time2 = chrono::duration<double, std::nano>(end2 - start2).count();
    
    // 测试有缓存污染，有预取
    uint64_t sum3 = 0;
    auto start3 = chrono::high_resolution_clock::now();
    for (size_t i = 0; i < indices.size(); ++i) {
        int idx = indices[i];
        
        // 预取下一个元素
        if (i + 1 < indices.size()) {
            PREFETCH(&g_stock_data[indices[i + 1]]);
            PREFETCH(&g_realtime_data[indices[i + 1]]);
        }
        
        // 每100次访问污染一次缓存
        if (i % 100 == 0) {
            for (size_t j = 0; j < POLLUTION_SIZE; j += 64) {
                pollution_data[j] = static_cast<char>(j);
            }
        }
        
        if (g_stock_data[idx].is_active) {
            sum3 += g_stock_data[idx].price;
            if (g_realtime_data[idx].current_volume > g_stock_data[idx].threshold) {
                sum3 += g_realtime_data[idx].current_volume;
            }
        }
    }
    auto end3 = chrono::high_resolution_clock::now();
    double time3 = chrono::duration<double, std::nano>(end3 - start3).count();
    
    // 输出结果
    cout << fixed << setprecision(2);
    cout << "无缓存污染:       " << (time1 / indices.size()) << " ns/op" << endl;
    cout << "有缓存污染，无预取: " << (time2 / indices.size()) << " ns/op (慢 " 
         << ((time2 - time1) / time1 * 100) << "%)" << endl;
    cout << "有缓存污染，有预取: " << (time3 / indices.size()) << " ns/op (慢 " 
         << ((time3 - time1) / time1 * 100) << "%)" << endl;
    cout << "预取改善:         " << ((time2 - time3) / time2 * 100) << "%" << endl;
    
    // 验证结果一致性
    if (sum1 != sum2 || sum1 != sum3) {
        cout << "警告: 不同方法的结果不一致!" << endl;
    }
    
    cout << endl;
}

int main() {
    // 设置控制台编码为UTF-8，解决中文乱码问题
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
#endif

    cout << "预取(PREFETCH)指令性能测试" << endl;
    cout << "测试迭代次数: " << TEST_ITERATIONS << endl;
    cout << "最大股票数量: " << MAX_SYMBOLS << endl;
    cout << "结构体大小: " << sizeof(StockData) << " 字节 (StockData), " 
         << sizeof(RealTimeData) << " 字节 (RealTimeData)" << endl << endl;
    
    // 初始化测试数据
    init_test_data(16);  // 默认16个活跃股票
    
    // 测试不同访问模式
    test_different_access_patterns();
    
    // 测试不同活跃股票数量
    test_different_active_counts();
    
    // 测试缓存污染的影响
    test_cache_pollution();
    
    cout << "按任意键退出..." << endl;
    getchar();
    
    return 0;
} 