#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <sys/time.h>
#include <time.h>
#include <iomanip>
#include "include/fast_string_builder.cpp"

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

#include "simple_strategy.cc"

// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {

    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {

        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {

        return 0;
    }
}

using namespace std;

// 简单的计时函数，传入函数指针或lambda，返回执行时间（微秒）
template<typename Func>
long long measureTime(Func func) {
    struct timeval tv;
    long long start, end;

    gettimeofday(&tv, NULL);
    start = tv.tv_usec;

    func();  // 执行传入的函数

    gettimeofday(&tv, NULL);
    end = tv.tv_usec;

    return end - start;
}

// 测量CPU周期数的函数，传入函数指针或lambda，返回执行的CPU周期数
template<typename Func>
long long measureCpuCycles(Func func) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();  // Windows使用__rdtsc()
#else
    start_cycles = __rdtsc();  // Linux也使用__rdtsc()
#endif

    func();  // 执行传入的函数

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    return end_cycles - start_cycles;
}

// 测量CPU周期数的函数（10000次平均），传入函数指针或lambda，返回平均CPU周期数
template<typename Func>
double measureCpuCyclesAverage(Func func, int iterations = 10000) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();
#else
    start_cycles = __rdtsc();
#endif

    // 执行10000次
    for(int i = 0; i < iterations; i++) {
        func();
    }

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    long long total_cycles = end_cycles - start_cycles;
    cout << static_cast<double>(total_cycles) / iterations << endl;
    return static_cast<double>(total_cycles) / iterations;
}


int main() {

    measureCpuCyclesAverage([]() {

        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
                 "1", "tv.tv_usec", (long long) 1, 1, 1);
    });

    measureCpuCyclesAverage([]() {
        FAST_LOG_START(g_log_buffer).
        addLiteral("order2: ")
               .addString("symbol")
               .addLiteral(", cost:")
               .addLong(1)
               .addLiteral(" us, T: ")
               .addLong(1)
               .addLiteral(", V:")
               .addLong(1)
               .addLiteral(", Hold:")
               .addLong(1)
        ;
        FAST_LOG_FINISH();
    });


    return 0;
}