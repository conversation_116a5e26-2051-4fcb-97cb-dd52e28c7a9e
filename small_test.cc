#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <sys/time.h>
#include <time.h>
#include <iomanip>

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

#include "simple_strategy.cc"
// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {

    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {

        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {

        return 0;
    }
}

using namespace std;

// 简单的计时函数，传入函数指针或lambda，返回执行时间（微秒）
template<typename Func>
long long measureTime(Func func) {
    struct timeval tv;
    long long start, end;

    gettimeofday(&tv, NULL);
    start = tv.tv_usec;

    func();  // 执行传入的函数

    gettimeofday(&tv, NULL);
    end = tv.tv_usec;

    return end - start;
}

// 测量CPU周期数的函数，传入函数指针或lambda，返回执行的CPU周期数
template<typename Func>
long long measureCpuCycles(Func func) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();  // Windows使用__rdtsc()
#else
    start_cycles = __rdtsc();  // Linux也使用__rdtsc()
#endif

    func();  // 执行传入的函数

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    return end_cycles - start_cycles;
}

// 测量CPU周期数的函数（10000次平均），传入函数指针或lambda，返回平均CPU周期数
template<typename Func>
double measureCpuCyclesAverage(Func func, int iterations = 10000) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();
#else
    start_cycles = __rdtsc();
#endif

    // 执行10000次
    for(int i = 0; i < iterations; i++) {
        func();
    }

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    long long total_cycles = end_cycles - start_cycles;
    return static_cast<double>(total_cycles) / iterations;
}

// 快速整数转字符串函数（支持负数）
int fastLongToString(char* buf, long long value) {
    if (value == 0) {
        buf[0] = '0';
        return 1;
    }

    char temp[32];
    int len = 0;
    bool negative = value < 0;

    if (negative) {
        value = -value;
    }

    // 转换数字
    while (value > 0) {
        temp[len++] = '0' + (value % 10);
        value /= 10;
    }

    int pos = 0;
    if (negative) {
        buf[pos++] = '-';
    }

    // 反转数字
    for (int i = len - 1; i >= 0; i--) {
        buf[pos++] = temp[i];
    }

    return pos;
}

// 通用的高效字符串构造器
class FastStringBuilder {
private:
    char* buffer;
    char* current;
    size_t capacity;

public:
    FastStringBuilder(char* buf, size_t cap) : buffer(buf), current(buf), capacity(cap) {}

    // 重置构造器
    void reset() {
        current = buffer;
    }

    // 添加字符串字面量
    FastStringBuilder& addLiteral(const char* str) {
        while (*str && (current - buffer) < capacity - 1) {
            *current++ = *str++;
        }
        return *this;
    }

    // 添加字符串变量
    FastStringBuilder& addString(const char* str) {
        if (str) {
            while (*str && (current - buffer) < capacity - 1) {
                *current++ = *str++;
            }
        }
        return *this;
    }

    // 添加长整数
    FastStringBuilder& addLong(long long value) {
        current += fastLongToString(current, value);
        return *this;
    }

    // 添加整数
    FastStringBuilder& addInt(int value) {
        current += fastLongToString(current, (long long)value);
        return *this;
    }

    // 添加单个字符
    FastStringBuilder& addChar(char c) {
        if ((current - buffer) < capacity - 1) {
            *current++ = c;
        }
        return *this;
    }

    // 完成构造
    int finish() {
        *current = '\0';
        return current - buffer;
    }

    // 获取当前长度
    int length() const {
        return current - buffer;
    }
};

// 宏定义简化使用
#define FAST_LOG_START(buf) FastStringBuilder builder(buf, sizeof(buf)); builder
#define FAST_LOG_FINISH() builder.finish()

// 示例：通用日志构造函数
int buildLogString(char* buffer, size_t bufSize, const char* prefix, const char* symbol,
                   const char* field1_name, long long field1_value,
                   const char* field2_name, long long field2_value,
                   const char* field3_name, long long field3_value,
                   const char* field4_name, long long field4_value) {
    FastStringBuilder builder(buffer, bufSize);

    return builder.addLiteral(prefix)
                  .addString(symbol)
                  .addLiteral(", ")
                  .addLiteral(field1_name)
                  .addLong(field1_value)
                  .addLiteral(", ")
                  .addLiteral(field2_name)
                  .addLong(field2_value)
                  .addLiteral(", ")
                  .addLiteral(field3_name)
                  .addLong(field3_value)
                  .addLiteral(", ")
                  .addLiteral(field4_name)
                  .addLong(field4_value)
                  .finish();
}

// 更灵活的模板版本
template<typename... Args>
int buildCustomLogString(char* buffer, size_t bufSize, Args... args) {
    FastStringBuilder builder(buffer, bufSize);
    (builder.addString(args), ...);  // C++17 fold expression
    return builder.finish();
}

// 使用外部定义的g_log_buffer
extern char g_log_buffer[512];

int main() {
    cout << "通用字符串构造性能测试 (10000次平均)" << endl;
    cout << "========================================" << endl;

    // 测试数据
    const char* symbol = "SH.600036";
    long long cost = 123456;
    long long T = 93045123456LL;
    long long V = 987654321LL;
    long long Hold = 555666777LL;

    // 方法1: 原始snprintf
    double cycles1 = measureCpuCyclesAverage([&]() {
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
                 symbol, cost, T, V, Hold);
    });

    // 方法2: 通用构造器 - 链式调用
    double cycles2 = measureCpuCyclesAverage([&]() {
        FastStringBuilder builder(g_log_buffer, 512);
        builder.addLiteral("order2: ")
               .addString(symbol)
               .addLiteral(", cost:")
               .addLong(cost)
               .addLiteral(" us, T: ")
               .addLong(T)
               .addLiteral(", V:")
               .addLong(V)
               .addLiteral(", Hold:")
               .addLong(Hold)
               .finish();
    });

    // 方法3: 通用构造器 - 函数封装
    double cycles3 = measureCpuCyclesAverage([&]() {
        buildLogString(g_log_buffer, 512, "order2: ", symbol,
                      "cost:", cost, " us, T: ", T,
                      ", V:", V, ", Hold:", Hold);
    });

    // 方法4: 宏简化版本
    double cycles4 = measureCpuCyclesAverage([&]() {
        FAST_LOG_START(g_log_buffer)
            .addLiteral("order2: ")
            .addString(symbol)
            .addLiteral(", cost:")
            .addLong(cost)
            .addLiteral(" us, T: ")
            .addLong(T)
            .addLiteral(", V:")
            .addLong(V)
            .addLiteral(", Hold:")
            .addLong(Hold);
        FAST_LOG_FINISH();
    });

    // 输出结果
    cout << fixed << setprecision(1);
    cout << "方法1 (snprintf):         " << cycles1 << " CPU周期" << endl;
    cout << "方法2 (链式构造器):       " << cycles2 << " CPU周期" << endl;
    cout << "方法3 (函数封装):         " << cycles3 << " CPU周期" << endl;
    cout << "方法4 (宏简化):           " << cycles4 << " CPU周期" << endl;

    // 验证结果正确性
    cout << endl << "结果验证:" << endl;

    snprintf(g_log_buffer, sizeof(g_log_buffer),
             "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
             symbol, cost, T, V, Hold);
    cout << "snprintf结果:     " << g_log_buffer << endl;

    FastStringBuilder builder(g_log_buffer, 512);
    builder.addLiteral("order2: ")
           .addString(symbol)
           .addLiteral(", cost:")
           .addLong(cost)
           .addLiteral(" us, T: ")
           .addLong(T)
           .addLiteral(", V:")
           .addLong(V)
           .addLiteral(", Hold:")
           .addLong(Hold)
           .finish();
    cout << "链式构造器结果:   " << g_log_buffer << endl;

    // 找出最快的方法
    vector<pair<string, double>> results = {
        {"snprintf", cycles1},
        {"链式构造器", cycles2},
        {"函数封装", cycles3},
        {"宏简化", cycles4}
    };

    auto fastest = min_element(results.begin(), results.end(),
        [](const auto& a, const auto& b) { return a.second < b.second; });

    cout << endl << "最快的方法是: " << fastest->first << " (平均 " << fastest->second << " CPU周期)" << endl;

    // 计算加速比
    cout << endl << "相对于snprintf的加速比:" << endl;
    for(const auto& result : results) {
        if(result.first != "snprintf") {
            double speedup = cycles1 / result.second;
            cout << result.first << ": " << speedup << "x 倍" << endl;
        }
    }

    // 演示不同格式的日志
    cout << endl << "========================================" << endl;
    cout << "演示不同格式的日志构造:" << endl;
    cout << "========================================" << endl;

    // 示例1: 错误日志
    FastStringBuilder(g_log_buffer, 512)
        .addLiteral("ERROR: ")
        .addString("Connection failed")
        .addLiteral(" at ")
        .addLong(1234567890)
        .addLiteral(" ms")
        .finish();
    cout << "错误日志: " << g_log_buffer << endl;

    // 示例2: 性能日志
    FastStringBuilder(g_log_buffer, 512)
        .addLiteral("PERF: ")
        .addString("ProcessOrder")
        .addLiteral(" took ")
        .addLong(156)
        .addLiteral(" us, CPU: ")
        .addLong(2340)
        .addLiteral(" cycles")
        .finish();
    cout << "性能日志: " << g_log_buffer << endl;

    // 示例3: 交易日志
    FastStringBuilder(g_log_buffer, 512)
        .addLiteral("TRADE: ")
        .addString("BUY")
        .addLiteral(" ")
        .addString("AAPL")
        .addLiteral(" qty:")
        .addLong(100)
        .addLiteral(" price:")
        .addLong(15025)  // 150.25 * 100
        .addLiteral(" time:")
        .addLong(93045123456LL)
        .finish();
    cout << "交易日志: " << g_log_buffer << endl;

    return 0;
}