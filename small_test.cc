#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <sys/time.h>
#include <time.h>
#include <iomanip>

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

#include "simple_strategy.cc"
// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {

    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {

        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {

        return 0;
    }
}

using namespace std;

// 简单的计时函数，传入函数指针或lambda，返回执行时间（微秒）
template<typename Func>
long long measureTime(Func func) {
    struct timeval tv;
    long long start, end;

    gettimeofday(&tv, NULL);
    start = tv.tv_usec;

    func();  // 执行传入的函数

    gettimeofday(&tv, NULL);
    end = tv.tv_usec;

    return end - start;
}

// 测量CPU周期数的函数，传入函数指针或lambda，返回执行的CPU周期数
template<typename Func>
long long measureCpuCycles(Func func) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();  // Windows使用__rdtsc()
#else
    start_cycles = __rdtsc();  // Linux也使用__rdtsc()
#endif

    func();  // 执行传入的函数

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    return end_cycles - start_cycles;
}

// 测量CPU周期数的函数（10000次平均），传入函数指针或lambda，返回平均CPU周期数
template<typename Func>
double measureCpuCyclesAverage(Func func, int iterations = 10000) {
    long long start_cycles, end_cycles;

#ifdef _WIN32
    start_cycles = __rdtsc();
#else
    start_cycles = __rdtsc();
#endif

    // 执行10000次
    for(int i = 0; i < iterations; i++) {
        func();
    }

#ifdef _WIN32
    end_cycles = __rdtsc();
#else
    end_cycles = __rdtsc();
#endif

    long long total_cycles = end_cycles - start_cycles;
    return static_cast<double>(total_cycles) / iterations;
}

// 快速整数转字符串函数（支持负数）
int fastLongToString(char* buf, long long value) {
    if (value == 0) {
        buf[0] = '0';
        return 1;
    }

    char temp[32];
    int len = 0;
    bool negative = value < 0;

    if (negative) {
        value = -value;
    }

    // 转换数字
    while (value > 0) {
        temp[len++] = '0' + (value % 10);
        value /= 10;
    }

    int pos = 0;
    if (negative) {
        buf[pos++] = '-';
    }

    // 反转数字
    for (int i = len - 1; i >= 0; i--) {
        buf[pos++] = temp[i];
    }

    return pos;
}

// 优化的日志字符串构造函数
// 格式: "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld"
int fastLogString(char* buffer, const char* symbol, long long cost, long long T, long long V, long long Hold) {
    char* p = buffer;

    // "order2: " - 直接内联
    *p++ = 'o'; *p++ = 'r'; *p++ = 'd'; *p++ = 'e'; *p++ = 'r'; *p++ = '2'; *p++ = ':'; *p++ = ' ';

    // symbol - 内联拷贝
    const char* s = symbol;
    while (*s) *p++ = *s++;

    // ", cost:" - 直接内联
    *p++ = ','; *p++ = ' '; *p++ = 'c'; *p++ = 'o'; *p++ = 's'; *p++ = 't'; *p++ = ':';

    // cost value
    p += fastLongToString(p, cost);

    // " us, T: " - 直接内联
    *p++ = ' '; *p++ = 'u'; *p++ = 's'; *p++ = ','; *p++ = ' '; *p++ = 'T'; *p++ = ':'; *p++ = ' ';

    // T value
    p += fastLongToString(p, T);

    // ", V:" - 直接内联
    *p++ = ','; *p++ = ' '; *p++ = 'V'; *p++ = ':';

    // V value
    p += fastLongToString(p, V);

    // ", Hold:" - 直接内联
    *p++ = ','; *p++ = ' '; *p++ = 'H'; *p++ = 'o'; *p++ = 'l'; *p++ = 'd'; *p++ = ':';

    // Hold value
    p += fastLongToString(p, Hold);

    *p = '\0';
    return p - buffer;
}

// 使用外部定义的g_log_buffer
extern char g_log_buffer[512];

int main() {
    cout << "日志字符串构造性能测试 (10000次平均)" << endl;
    cout << "======================================" << endl;

    // 测试数据
    const char* symbol = "SH.600036";
    long long cost = 123456;
    long long T = 93045123456LL;
    long long V = 987654321LL;
    long long Hold = 555666777LL;

    // 方法1: 原始snprintf
    double cycles1 = measureCpuCyclesAverage([&]() {
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
                 symbol, cost, T, V, Hold);
    });

    // 方法2: 优化的手动构造
    double cycles2 = measureCpuCyclesAverage([&]() {
        fastLogString(g_log_buffer, symbol, cost, T, V, Hold);
    });

    // 输出结果
    cout << fixed << setprecision(1);
    cout << "方法1 (snprintf):     " << cycles1 << " CPU周期" << endl;
    cout << "方法2 (手动构造):     " << cycles2 << " CPU周期" << endl;

    // 验证结果正确性
    cout << endl << "结果验证:" << endl;
    snprintf(g_log_buffer, sizeof(g_log_buffer),
             "order2: %s, cost:%lld us, T: %lld, V:%lld, Hold:%lld",
             symbol, cost, T, V, Hold);
    cout << "snprintf结果:   " << g_log_buffer << endl;

    fastLogString(g_log_buffer, symbol, cost, T, V, Hold);
    cout << "手动构造结果:   " << g_log_buffer << endl;

    // 计算加速比
    double speedup = cycles1 / cycles2;
    cout << endl << "加速比: " << speedup << "x 倍" << endl;

    // 计算时间节省（基于i7-14700 @ 3.5GHz）
    double time_saved_ns = (cycles1 - cycles2) / 3.5;
    cout << "每次调用节省时间: " << time_saved_ns << " ns" << endl;

    return 0;
}