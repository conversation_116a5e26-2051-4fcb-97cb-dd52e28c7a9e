# 简单计时函数使用说明

## 函数定义

```cpp
template<typename Func>
long long measureTime(Func func) {
    struct timeval tv;
    long long start, end;
    
    gettimeofday(&tv, NULL);
    start = tv.tv_usec;
    
    func();  // 执行传入的函数
    
    gettimeofday(&tv, NULL);
    end = tv.tv_usec;
    
    return end - start;
}
```

## 使用方法

### 1. 测量无参数函数
```cpp
long long duration = measureTime(str_datetime_now);
cout << "执行时间: " << duration << " 微秒" << endl;
```

### 2. 测量 lambda 表达式
```cpp
long long duration = measureTime([]() {
    // 你的代码
    str_datetime_now();
});
cout << "执行时间: " << duration << " 微秒" << endl;
```

### 3. 测量带参数的函数（使用 lambda 包装）
```cpp
long long duration = measureTime([&]() {
    someFunction(param1, param2);
});
cout << "执行时间: " << duration << " 微秒" << endl;
```

### 4. 测量代码块
```cpp
long long duration = measureTime([]() {
    // 任意代码块
    for(int i = 0; i < 1000; i++) {
        // 做一些计算
    }
});
cout << "执行时间: " << duration << " 微秒" << endl;
```

## 替换原有代码

### 原来的代码：
```cpp
gettimeofday(&tv, NULL);
start = tv.tv_usec;

str_datetime_now();

gettimeofday(&tv, NULL);
long long end = tv.tv_usec;
cout << end - start;
```

### 新的代码：
```cpp
long long duration = measureTime([]() {
    str_datetime_now();
});
cout << "执行时间: " << duration << " 微秒" << endl;
```

## 优点

1. **代码更简洁**：一行代码就能完成计时
2. **可重用**：可以测量任何函数或代码块
3. **类型安全**：使用模板，支持各种函数类型
4. **易于使用**：只需要传入要测量的函数即可

## 注意事项

- 返回值单位是微秒
- 使用 `gettimeofday()` 保持与原代码一致
- 适用于测量执行时间较短的函数
- 如果需要更高精度或跨平台支持，可以考虑使用 `std::chrono`
