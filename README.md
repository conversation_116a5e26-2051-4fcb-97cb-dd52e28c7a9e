# F4 高频交易策略系统

## 项目概述

F4 是一个高性能的涨停板封单检测和板块监控交易策略系统，支持上海和深圳两个市场的实时行情处理和自动化交易。

## 核心功能

### 1. 涨停板封单检测
- 支持 SH/SZ 双市场的涨停板封单检测
- 基于逐笔委托和成交数据的实时封单量计算
- 多种下单模式：快速模式、慢排版模式、FCode模式
- 智能撤单机制：基于封单量变化和时间窗口的风控撤单

### 2. 300ms滑动窗口算法 🚀
本系统的核心创新功能，解决了固定时间窗口遗漏交易机会的问题。

#### 2.1 算法优势
- **跨边界捕获**：能够捕获跨越固定300ms窗口边界的交易量累积
- **高性能计算**：O(1)增量计算，适合高频交易场景
- **精确控制**：基于10ms精度的时间戳数组，40个槽位覆盖400ms
- **市场差异**：SH市场基于成交数据，SZ市场基于委托数据

#### 2.2 技术实现
```cpp
struct SlidingWindow {
    static const int WINDOW_SLOTS = 40;  // 40个时间槽，10ms精度
    int64_t volume_slots[WINDOW_SLOTS];  // 静态分配，无动态内存
    int64_t total_volume_300ms;          // 当前窗口总量
    int64_t last_update_time;            // 增量计算优化

    // O(1)增量更新
    void addVolumeToSlidingWindow(int64_t timestamp, int64_t volume);
    // 智能过期清理
    void updateSlidingWindowIncremental(int64_t current_time);
};
```

#### 2.3 算法对比
```
固定窗口问题：
时间轴: [0-300ms] [300-600ms]
数据:   [1500万]  [1500万]
结果:   都不满足2000万条件 ❌

滑动窗口解决：
时间轴: [0-300ms] [300-600ms]
数据:   [1500万]  [1500万]
滑动:     [----300ms滑动窗口----]
结果:     3000万 > 2000万 ✅
```

### 3. 板块监控系统 ⭐
板块监控是本系统的重要功能，实现了板块内股票的智能发现和互斥下单。

### 4. 涨跌停幅度分组 🎯
基于股票涨跌停幅度的智能分组管理，实现10cm和20cm股票的独立监控和下单。

#### 3.1 核心机制
- **涨幅位图存储**：使用高性能位图存储涨幅超过6%的股票
- **自动发现订阅**：通过定时任务自动发现并订阅板块内的强势股票
- **板块内互斥**：确保每个板块只下单一只股票，避免同板块重复下单

#### 3.2 技术实现
```cpp
// 板块分组结构
struct SectorGroup {
    char group_name[32];                    // 分组名称
    char codes[MAX_CODES_PER_GROUP][12];   // 股票代码数组
    int code_count;                        // 股票数量
    bool is_active;                        // 是否激活监控
    bool has_ordered;                      // 分组内是否已有下单
};

// 涨幅位图系统
static uint64_t g_gain_stock_bitmap[BITMAP_WORDS];  // 支持100万只股票的位图

// 板块监控定时任务
void do_block_monitor() {
    // 检查板块内股票是否有涨幅超标的
    // 自动订阅符合条件的股票
    // 实现板块内互斥下单逻辑
}
```

#### 3.3 工作流程
1. **实时监控**：在 `OnSecurityTick` 中检测股票涨幅超过6%时设置位图标记
2. **定时扫描**：`do_block_monitor` 定时任务扫描板块内股票的位图标记
3. **自动订阅**：发现符合条件的股票后自动调用 `SubscribeLimitUpDetection`
4. **互斥下单**：通过 `sector_group_index` 关联和 `has_ordered` 标记实现板块内互斥
5. **智能排除**：板块内已有下单后，其他股票自动被排除监控

#### 4.1 涨跌停幅度识别
- **10cm股票**：主板股票（排除300开头的创业板、688开头的科创板、8/4开头的北交所）
- **20cm股票**：创业板(300)、科创板(688)、北交所(8/4开头)股票

#### 4.2 自动分组机制
```cpp
// 涨跌停幅度类型
enum LimitType {
    LIMIT_10CM = 0,  // 10%涨跌停
    LIMIT_20CM = 1   // 20%涨跌停
};

// 自动分组：原配置 -> 两个独立分组
"新能源汽车" -> "新能源汽车_10CM" + "新能源汽车_20CM"
```

#### 4.3 独立下单逻辑
- **并行下单**：10cm和20cm分组可以同时下单，互不影响
- **分组内互斥**：同一分组内只能下单一只股票
- **类型匹配**：只监控与分组类型匹配的股票

### 5. 高性能优化
- **滑动窗口优化**：O(1)增量计算替代O(40)全量重新计算，大幅降低延迟
- **缓存行对齐**：关键数据结构使用64字节对齐优化
- **紧凑数组**：只存储活跃监控的股票索引，提高查找效率
- **位图算法**：O(1)时间复杂度的涨幅股票标记和查询
- **内存预分配**：避免动态内存分配，减少延迟
- **静态分配**：滑动窗口使用静态数组，无动态内存分配

## 测试系统

### 300ms滑动窗口测试 🚀
验证滑动窗口算法的核心功能和性能优化效果。

#### 滑动窗口算法测试场景
```
测试场景1：跨边界累积测试
时间点：93000150ms (1500万股) + 93000450ms (1500万股)
预期：滑动窗口捕获3000万股累积，触发下单
验证：固定窗口会遗漏，滑动窗口能正确捕获

测试场景2：高频数据性能测试
数据：每10ms一笔交易，持续500ms (50笔交易)
预期：O(1)增量计算，低延迟处理高频数据
验证：性能优化效果，无延迟累积
```

### 板块监控测试
项目包含完整的板块监控功能测试，验证真实交易场景。

### 涨跌停幅度分组测试
验证10cm和20cm股票的自动分组和独立下单逻辑。

#### 板块监控测试场景
```
测试板块：新能源汽车（10只股票）
测试逻辑：
1. SH.600104 最先达到6%涨幅，触发位图标记
2. 板块监控定时任务自动发现并订阅 SH.600104
3. SH.600104 达到7%涨幅并涨停，累积足够封单量后下单
4. SH.600066 后续也达到条件，但因板块内已有下单被排除
5. 验证板块内互斥下单逻辑
```

#### 涨跌停幅度分组测试场景
```
测试板块：新能源汽车混合（12只股票：6只10cm + 6只20cm）
测试逻辑：
1. 自动分组：新能源汽车混合_10CM + 新能源汽车混合_20CM
2. SH.600104 (10cm) 达到条件并下单
3. SZ.300750 (20cm) 达到条件并下单（独立分组，可并行）
4. SZ.002594 (10cm) 被10cm分组互斥逻辑排除
5. SZ.300661 (20cm) 被20cm分组互斥逻辑排除
6. 验证10cm和20cm分组的独立性和内部互斥性
```

#### 运行测试
```bash
# 编译测试
g++ -std=c++17 -I. -Iinclude strategy_test.cc -o strategy_test

# 运行板块监控测试
./strategy_test
```

#### 测试结果验证
- ✅ 板块配置加载成功
- ✅ 涨幅位图标记机制工作正常
- ✅ 板块监控定时任务自动发现并订阅股票
- ✅ 第一只股票(SH.600104)成功下单
- ✅ 板块内互斥下单逻辑工作正常
- ✅ 后续股票(SH.600066)因板块内已有下单而被排除

### 测试文件结构
```
# 300ms滑动窗口测试
test_sliding_window_300ms.txt       # 滑动窗口算法测试数据
test_sliding_window_performance.txt # 高频数据性能测试
test_no_order_scenarios.txt        # 边界条件测试（不应该下单的场景）

# 板块监控测试
test_sector_config.json             # 板块监控配置文件
test_sector_monitor.txt             # 板块监控测试数据

# 涨跌停幅度分组测试
test_limit_group_config.json        # 涨跌停幅度分组配置文件
test_limit_group_monitor.txt        # 涨跌停幅度分组测试数据

# 测试框架
strategy_test.cc                    # 测试框架和用例
strategy_test.h                     # 测试头文件
```

## 配置文件

### 板块配置示例

#### 单一类型板块配置
```json
[
  {
    "key": "SectorGroup",
    "value": {
      "group_name": "新能源汽车",
      "codes": ["600104", "002594", "000625", "002129", "600066", "002460"]
    }
  }
]
```

#### 混合类型板块配置（自动分组）
```json
[
  {
    "key": "SectorGroup",
    "value": {
      "group_name": "新能源汽车混合",
      "codes": [
        "600104", "002594", "000625", "002129", "600066", "002460",
        "300750", "300661", "300274", "688981", "688008", "688169"
      ]
    }
  }
]
```
*系统将自动分为：新能源汽车混合_10CM 和 新能源汽车混合_20CM 两个独立分组*

## 编译和运行

### 系统要求
- C++17 或更高版本
- 支持 x86_64 和 ARM64 架构
- macOS / Linux 系统

### 编译选项
```bash
g++ -std=c++17 -I. -Iinclude -O3 simple_strategy.cc -o strategy
```

### 性能特性
- **微秒级延迟**：关键路径延迟控制在微秒级别
- **高频数据处理**：支持每10ms一笔的极高频交易数据
- **O(1)滑动窗口**：增量计算算法，避免O(40)全量重新计算
- **高并发处理**：支持同时监控64只股票的实时行情
- **内存高效**：位图系统仅使用125KB内存支持100万只股票
- **零拷贝设计**：使用char[12]固定长度字符串避免动态分配

## 风控机制

### 多层风控体系
1. **成交金额风控**：1分钟内成交金额超过2000万自动撤单
2. **封单量风控**：封单量减少20%触发撤单
3. **时间窗口风控**：基于逐笔数据时间的撤单策略
4. **板块互斥风控**：防止同板块重复下单的资金风险

### 智能撤单策略
- **快速模式**：30ms后检查撤单，封单阈值5000万
- **慢排版模式**：3秒后检查撤单，封单阈值6000万
- **跨tick监控**：10秒内封单减少20%自动撤单

## 架构特点

### 模块化设计
- **策略核心**：`simple_strategy.cc` - 主要交易逻辑
- **测试框架**：`strategy_test.cc` - 完整的测试体系
- **API接口**：`md_api.h`, `trade_api.h`, `strategy_api.h`
- **性能优化**：`performance/` - 性能测试和优化工具

### 数据结构优化
- **缓存友好**：64字节对齐的数据结构
- **内存局部性**：紧凑数组和位图算法
- **零拷贝**：固定长度字符串和预分配内存

## 开发日志

详细的开发进展和功能更新请查看 [CHANGELOG.md](./CHANGELOG.md)

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

本项目采用 MIT 许可证。